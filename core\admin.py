# admin.py
from django.contrib import admin
from .models import (
    <PERSON><PERSON>, <PERSON><PERSON>, Case, Hearing, Appointment, Document,
    CaseNote, Task, Invoice, Payment, TimeEntry, Expense
)

# To make the admin more useful, we can customize how models are displayed.
@admin.register(Case)
class CaseAdmin(admin.ModelAdmin):
    list_display = ('title', 'start_date', 'status')
    list_filter = ('status', 'start_date')
    search_fields = ('title', 'description')
    filter_horizontal = ('lawyers', 'clients') # A better widget for ManyToMany fields

@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'phone')
    search_fields = ('name', 'email')

@admin.register(Lawyer)
class LawyerAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'specialization', 'phone') # __str__ will call the model's __str__ method
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 'specialization')

# You can register other models simply or with customization
admin.site.register(Hearing)
admin.site.register(Appointment)
admin.site.register(Document)
admin.site.register(CaseNote)
admin.site.register(Task)
admin.site.register(Invoice)
admin.site.register(Payment)
admin.site.register(TimeEntry)
admin.site.register(Expense)