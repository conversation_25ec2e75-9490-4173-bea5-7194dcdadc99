{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-12 col-md-8 col-lg-6">
            <div class="card shadow rounded-4 border-0">
                <div class="card-body p-4">
                    <h3 class="mb-4 text-center fw-bold">
                        <i class="bi bi-cash-coin text-success me-2"></i>
                        {% if is_update %}تعديل الأتعاب{% else %}إضافة أتعاب جديدة للقضية: <span class="text-primary">{{ case.case_number }}</span>{% endif %}
                    </h3>
                    <form method="post" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}
                        {% for field in form %}
                            <div class="mb-3">
                                <label class="form-label fw-semibold">{{ field.label }}</label>
                                {{ field }}
                                {% if field.help_text %}
                                    <div class="form-text">{{ field.help_text }}</div>
                                {% endif %}
                                {% if field.errors %}
                                    <div class="alert alert-danger py-1 px-2 mt-2 mb-0 small">{{ field.errors|striptags }}</div>
                                {% endif %}
                            </div>
                        {% endfor %}
                        <button type="submit" class="btn btn-gradient-gold w-100 py-2 fs-5 mt-2">
                            <i class="bi bi-check-circle me-1"></i>
                            {% if is_update %}تحديث الأتعاب{% else %}إضافة الأتعاب{% endif %}
                        </button>
                        <a href="{% url 'cases:detail' case.pk %}" class="btn btn-link w-100 mt-2">
                            <i class="bi bi-arrow-right-circle"></i> العودة لتفاصيل القضية
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
<style>
.btn-gradient-gold {
    background: linear-gradient(90deg, #ffd600 0%, #ffb300 100%);
    color: #222b45;
    border: none;
    font-weight: bold;
    transition: box-shadow 0.2s;
}
.btn-gradient-gold:hover {
    box-shadow: 0 2px 12px #ffd60055;
    color: #151a30;
}
</style>
{% endblock %} 