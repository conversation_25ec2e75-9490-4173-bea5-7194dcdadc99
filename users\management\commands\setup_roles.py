from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType

ROLES = {
    'Admin': {
        'permissions': '__all__',
    },
    'Lawyer': {
        'permissions': [
            'view_case', 'change_case', 'view_document', 'change_document',
            'view_appointment', 'change_appointment',
            'view_client',
        ],
    },
    'Receptionist': {
        'permissions': [
            'add_client', 'change_client', 'view_client',
            'add_appointment', 'change_appointment', 'view_appointment',
        ],
    },
    'User': {
        'permissions': ['view_case', 'view_document', 'view_appointment', 'view_client'],
    },
}

class Command(BaseCommand):
    help = 'Setup default roles (groups) and permissions for the system.'

    def handle(self, *args, **kwargs):
        for role, data in ROLES.items():
            group, created = Group.objects.get_or_create(name=role)
            if data['permissions'] == '__all__':
                perms = Permission.objects.all()
            else:
                perms = Permission.objects.filter(codename__in=data['permissions'])
            group.permissions.set(perms)
            group.save()
            self.stdout.write(self.style.SUCCESS(f'Group "{role}" updated with permissions.'))
        self.stdout.write(self.style.SUCCESS('All roles and permissions set up successfully.')) 