{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-12 col-md-8 col-lg-6">
            <div class="card shadow rounded-4 border-0">
                <div class="card-body p-4">
                    <h3 class="mb-4 text-center fw-bold">
                        <i class="bi bi-briefcase text-primary me-2"></i>
                        تفاصيل القضية
                    </h3>
                    <ul class="list-group list-group-flush mb-4">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-hash me-2 text-secondary"></i>رقم القضية:</span>
                            <span class="fw-bold">{{ case.case_number }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-type me-2 text-secondary"></i>العنوان:</span>
                            <span class="fw-bold">{{ case.title }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-card-text me-2 text-secondary"></i>الوصف:</span>
                            <span class="fw-bold">{{ case.description }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-person me-2 text-secondary"></i>العميل:</span>
                            <span class="fw-bold">{{ case.client }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-person-badge me-2 text-secondary"></i>المحامي:</span>
                            <span class="fw-bold">{{ case.lawyer }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-calendar2-week me-2 text-secondary"></i>تاريخ البدء:</span>
                            <span class="fw-bold">{{ case.start_date }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-calendar2-check me-2 text-secondary"></i>تاريخ الإغلاق:</span>
                            <span class="fw-bold">{{ case.end_date }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-info-circle me-2 text-secondary"></i>الحالة:</span>
                            <span class="fw-bold">{{ case.get_status_display }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-tag me-2 text-secondary"></i>نوع القضية:</span>
                            <span class="fw-bold">{{ case.get_case_type_display }}</span>
                        </li>
                    </ul>
                    <div class="d-flex justify-content-between gap-2 mb-4">
                        <a href="{% url 'cases:edit' case.pk %}" class="btn btn-primary flex-fill">
                            <i class="bi bi-pencil-square"></i> تعديل
                        </a>
                        <a href="{% url 'cases:delete' case.pk %}" class="btn btn-danger flex-fill">
                            <i class="bi bi-trash"></i> حذف
                        </a>
                        <a href="{% url 'cases:case_accounts' case.pk %}" class="btn btn-info flex-fill">
                            <i class="bi bi-cash-coin"></i> حسابات القضية
                        </a>
                        <a href="{% url 'cases:list' %}" class="btn btn-link flex-fill">
                            <i class="bi bi-arrow-right-circle"></i> العودة للقائمة
                        </a>
                    </div>
                    <!-- جلسات القضية -->
                    <h5 class="fw-bold mt-4 mb-3"><i class="bi bi-calendar-event me-2"></i>الجلسات المرتبطة</h5>
                    <div class="mb-3 text-end">
                        <a href="{% url 'cases:hearing_add' case.pk %}" class="btn btn-success btn-sm px-4">
                            <i class="bi bi-plus-circle"></i> إضافة جلسة جديدة
                        </a>
                    </div>
                    {% if hearings %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover align-middle text-center">
                                <thead class="table-light">
                                    <tr>
                                        <th>تاريخ ووقت الجلسة</th>
                                        <th>المكان</th>
                                        <th>الوصف</th>
                                        <th>تفاصيل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for hearing in hearings %}
                                    <tr>
                                        <td>{{ hearing.date|date:"Y-m-d H:i" }}</td>
                                        <td>{{ hearing.location|default:"-" }}</td>
                                        <td>{{ hearing.description|default:"-"|truncatechars:30 }}</td>
                                        <td>
                                            <a href="{% url 'cases:hearing_detail' hearing.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i> عرض
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info text-center">لا توجد جلسات مرتبطة بهذه القضية بعد.</div>
                    {% endif %}
                    <!-- أتعاب القضية -->
                    <h5 class="fw-bold mt-4 mb-3"><i class="bi bi-cash-coin me-2"></i>الأتعاب والحسابات</h5>
                    <div class="mb-3 text-end">
                        <a href="{% url 'cases:fee_add' case.pk %}" class="btn btn-success btn-sm px-4">
                            <i class="bi bi-plus-circle"></i> إضافة أتعاب جديدة
                        </a>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 mb-2">
                            <div class="alert alert-success text-center mb-0">المدفوع: <strong>{{ fees_paid|floatformat:2 }}</strong> ج.م</div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="alert alert-warning text-center mb-0">المتبقي: <strong>{{ fees_unpaid|floatformat:2 }}</strong> ج.م</div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="alert alert-info text-center mb-0">إجمالي الأتعاب: <strong>{{ fees_total|floatformat:2 }}</strong> ج.م</div>
                        </div>
                    </div>
                    {% if fees %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover align-middle text-center">
                                <thead class="table-light">
                                    <tr>
                                        <th>المبلغ</th>
                                        <th>النوع</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>تاريخ الدفع</th>
                                        <th>الحالة</th>
                                        <th>ملاحظات</th>
                                        <th>إيصال</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for fee in fees %}
                                    <tr>
                                        <td>{{ fee.amount|floatformat:2 }}</td>
                                        <td>{{ fee.get_fee_type_display }}</td>
                                        <td>{{ fee.due_date|default:"-" }}</td>
                                        <td>{{ fee.paid_date|default:"-" }}</td>
                                        <td>
                                            {% if fee.is_paid %}
                                                <span class="badge bg-success">مدفوع</span>
                                            {% else %}
                                                <span class="badge bg-warning text-dark">غير مدفوع</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ fee.note|default:"-" }}</td>
                                        <td>
                                            {% if fee.receipt %}
                                                <a href="{{ fee.receipt.url }}" target="_blank" class="btn btn-sm btn-outline-primary"><i class="bi bi-download"></i> تحميل</a>
                                            {% else %}-{% endif %}
                                            <a href="{% url 'cases:fee_edit' fee.pk %}" class="btn btn-sm btn-outline-secondary ms-1"><i class="bi bi-pencil"></i> تعديل</a>
                                            <a href="{% url 'cases:fee_delete' fee.pk %}" class="btn btn-sm btn-outline-danger ms-1"><i class="bi bi-trash"></i> حذف</a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info text-center">لا توجد أتعاب مسجلة لهذه القضية بعد.</div>
                    {% endif %}
                    <!-- دفعات القضية -->
                    <h5 class="fw-bold mt-4 mb-3"><i class="bi bi-cash-stack me-2"></i>دفعات مرتبطة بهذه القضية</h5>
                    {% if payments %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover align-middle text-center">
                                <thead class="table-light">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>المبلغ</th>
                                        <th>ملاحظات</th>
                                        <th>تفاصيل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for payment in payments %}
                                    <tr>
                                        <td>{{ payment.date }}</td>
                                        <td>{{ payment.amount|floatformat:2 }}</td>
                                        <td>{{ payment.notes|default:'-' }}</td>
                                        <td>
                                            <a href="{% url 'clients:payment_detail' payment.id %}" class="btn btn-outline-info btn-sm"><i class="bi bi-eye"></i> تفاصيل</a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info text-center">لا توجد دفعات مرتبطة بهذه القضية بعد.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
{% endblock %} 