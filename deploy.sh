#!/bin/bash

# Lawyers Office System Deployment Script
# This script automates the deployment process for the Lawyers Office System

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="lawyers-office-system"
DOCKER_COMPOSE_FILE="docker-compose.yml"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deployment.log"

# Functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    echo "[ERROR] $1" >> "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
    echo "[WARNING] $1" >> "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
    echo "[INFO] $1" >> "$LOG_FILE"
}

# Check if Docker is installed and running
check_docker() {
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker is not running. Please start Docker first."
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    log "Docker and Docker Compose are available"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p media
    mkdir -p static
    mkdir -p backups
    mkdir -p ssl
    
    log "Directories created successfully"
}

# Generate SSL certificates (self-signed for development)
generate_ssl_certificates() {
    if [ ! -f "./ssl/cert.pem" ] || [ ! -f "./ssl/key.pem" ]; then
        log "Generating SSL certificates..."
        
        openssl req -x509 -newkey rsa:4096 -keyout ./ssl/key.pem -out ./ssl/cert.pem -days 365 -nodes \
            -subj "/C=SA/ST=Riyadh/L=Riyadh/O=Lawyers Office/CN=localhost"
        
        log "SSL certificates generated"
    else
        log "SSL certificates already exist"
    fi
}

# Check environment variables
check_environment() {
    log "Checking environment variables..."
    
    if [ -z "$SECRET_KEY" ]; then
        warning "SECRET_KEY not set. Generating a random one..."
        export SECRET_KEY=$(python3 -c 'from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())')
    fi
    
    if [ -z "$DATABASE_URL" ]; then
        warning "DATABASE_URL not set. Using default SQL Server configuration..."
        export DATABASE_URL="mssql://sa:YourPassword123@db:1433/LawyersOfficeDB?driver=ODBC+Driver+18+for+SQL+Server&TrustServerCertificate=yes"
    fi
    
    if [ -z "$REDIS_URL" ]; then
        export REDIS_URL="redis://redis:6379/0"
    fi
    
    if [ -z "$ALLOWED_HOSTS" ]; then
        export ALLOWED_HOSTS="localhost,127.0.0.1"
    fi
    
    log "Environment variables checked"
}

# Backup existing data
backup_data() {
    if [ "$1" = "--backup" ]; then
        log "Creating backup..."
        
        BACKUP_TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
        BACKUP_PATH="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP"
        
        mkdir -p "$BACKUP_PATH"
        
        # Backup database
        if docker-compose ps | grep -q "lawyers_office_db"; then
            log "Backing up database..."
            docker-compose exec -T db sqlcmd -S localhost -U sa -P YourPassword123 -Q "BACKUP DATABASE LawyersOfficeDB TO DISK = '/var/opt/mssql/backup/LawyersOfficeDB_$BACKUP_TIMESTAMP.bak'"
            docker cp lawyers_office_db:/var/opt/mssql/backup/LawyersOfficeDB_$BACKUP_TIMESTAMP.bak "$BACKUP_PATH/"
        fi
        
        # Backup media files
        if [ -d "./media" ]; then
            log "Backing up media files..."
            cp -r ./media "$BACKUP_PATH/"
        fi
        
        log "Backup created at $BACKUP_PATH"
    fi
}

# Build and start services
deploy_services() {
    log "Building and starting services..."
    
    # Pull latest images
    docker-compose pull
    
    # Build custom images
    docker-compose build --no-cache
    
    # Start services
    docker-compose up -d
    
    log "Services started successfully"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    # Wait for database to be ready
    sleep 30
    
    # Run migrations
    docker-compose exec web python manage.py migrate --settings=Lawyers_Office_Sys.settings.production
    
    log "Database migrations completed"
}

# Collect static files
collect_static() {
    log "Collecting static files..."
    
    docker-compose exec web python manage.py collectstatic --noinput --settings=Lawyers_Office_Sys.settings.production
    
    log "Static files collected"
}

# Create superuser
create_superuser() {
    if [ "$1" = "--create-superuser" ]; then
        log "Creating superuser..."
        
        docker-compose exec web python manage.py createsuperuser --settings=Lawyers_Office_Sys.settings.production
        
        log "Superuser created"
    fi
}

# Load initial data
load_initial_data() {
    if [ "$1" = "--load-fixtures" ]; then
        log "Loading initial data..."
        
        # Load fixtures if they exist
        if [ -d "./fixtures" ]; then
            docker-compose exec web python manage.py loaddata fixtures/*.json --settings=Lawyers_Office_Sys.settings.production
        fi
        
        log "Initial data loaded"
    fi
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Wait for services to be ready
    sleep 10
    
    # Check web service
    if curl -f http://localhost:8000/health/ &> /dev/null; then
        log "Web service is healthy"
    else
        error "Web service health check failed"
    fi
    
    # Check database connection
    if docker-compose exec web python manage.py check --database default --settings=Lawyers_Office_Sys.settings.production &> /dev/null; then
        log "Database connection is healthy"
    else
        error "Database connection check failed"
    fi
    
    log "Health check completed successfully"
}

# Show deployment status
show_status() {
    log "Deployment Status:"
    echo ""
    docker-compose ps
    echo ""
    log "Services are accessible at:"
    log "- Web Application: http://localhost:8000"
    log "- Admin Panel: http://localhost:8000/admin/"
    log "- Flower (Celery Monitor): http://localhost:5555"
    echo ""
    log "Logs can be viewed with: docker-compose logs -f [service_name]"
}

# Cleanup old containers and images
cleanup() {
    if [ "$1" = "--cleanup" ]; then
        log "Cleaning up old containers and images..."
        
        docker system prune -f
        docker volume prune -f
        
        log "Cleanup completed"
    fi
}

# Main deployment function
main() {
    log "Starting deployment of $PROJECT_NAME..."
    
    # Parse command line arguments
    BACKUP=false
    CREATE_SUPERUSER=false
    LOAD_FIXTURES=false
    CLEANUP=false
    
    for arg in "$@"; do
        case $arg in
            --backup)
                BACKUP=true
                ;;
            --create-superuser)
                CREATE_SUPERUSER=true
                ;;
            --load-fixtures)
                LOAD_FIXTURES=true
                ;;
            --cleanup)
                CLEANUP=true
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --backup           Create backup before deployment"
                echo "  --create-superuser Create Django superuser"
                echo "  --load-fixtures    Load initial data fixtures"
                echo "  --cleanup          Clean up old Docker resources"
                echo "  --help             Show this help message"
                exit 0
                ;;
        esac
    done
    
    # Run deployment steps
    check_docker
    create_directories
    generate_ssl_certificates
    check_environment
    
    if [ "$BACKUP" = true ]; then
        backup_data --backup
    fi
    
    deploy_services
    run_migrations
    collect_static
    
    if [ "$CREATE_SUPERUSER" = true ]; then
        create_superuser --create-superuser
    fi
    
    if [ "$LOAD_FIXTURES" = true ]; then
        load_initial_data --load-fixtures
    fi
    
    health_check
    show_status
    
    if [ "$CLEANUP" = true ]; then
        cleanup --cleanup
    fi
    
    log "Deployment completed successfully!"
}

# Run main function with all arguments
main "$@"
