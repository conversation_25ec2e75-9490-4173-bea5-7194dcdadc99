from django import forms
from django.core.exceptions import ValidationError
from .models import Document, DocumentCategory, DocumentVersion
from .utils import validate_file
from cases.models import Case
from clients.models import Client

class DocumentForm(forms.ModelForm):
    """Form for uploading and editing documents"""
    
    class Meta:
        model = Document
        fields = [
            'name', 'description', 'file', 'case', 'client', 'category',
            'document_type', 'access_level', 'tags', 'notes'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المستند'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف مختصر للمستند'
            }),
            'file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx,.xls,.xlsx,.txt,.jpg,.jpeg,.png,.gif'
            }),
            'case': forms.Select(attrs={
                'class': 'form-select'
            }),
            'client': forms.Select(attrs={
                'class': 'form-select'
            }),
            'category': forms.Select(attrs={
                'class': 'form-select'
            }),
            'document_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'access_level': forms.Select(attrs={
                'class': 'form-select'
            }),
            'tags': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'العلامات (افصل بفاصلة)'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'ملاحظات إضافية'
            })
        }
        labels = {
            'name': 'اسم المستند',
            'description': 'الوصف',
            'file': 'الملف',
            'case': 'القضية',
            'client': 'العميل',
            'category': 'الفئة',
            'document_type': 'نوع المستند',
            'access_level': 'مستوى الوصول',
            'tags': 'العلامات',
            'notes': 'ملاحظات'
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Make some fields optional
        self.fields['description'].required = False
        self.fields['case'].required = False
        self.fields['client'].required = False
        self.fields['category'].required = False
        self.fields['tags'].required = False
        self.fields['notes'].required = False
        
        # Filter cases and clients based on user permissions
        if user and not user.is_superuser:
            if user.is_staff:
                # Staff can see cases they're assigned to
                self.fields['case'].queryset = Case.objects.filter(lawyer=user)
            else:
                # Regular users can't select cases
                self.fields['case'].queryset = Case.objects.none()

    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            is_valid, message = validate_file(file)
            if not is_valid:
                raise ValidationError(message)
        return file

    def clean(self):
        cleaned_data = super().clean()
        case = cleaned_data.get('case')
        client = cleaned_data.get('client')
        
        # At least one of case or client must be selected
        if not case and not client:
            raise ValidationError('يجب اختيار قضية أو عميل على الأقل')
        
        return cleaned_data


class DocumentCategoryForm(forms.ModelForm):
    """Form for creating and editing document categories"""
    
    class Meta:
        model = DocumentCategory
        fields = ['name', 'description', 'color', 'icon']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم الفئة'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف الفئة'
            }),
            'color': forms.TextInput(attrs={
                'class': 'form-control',
                'type': 'color'
            }),
            'icon': forms.Select(attrs={
                'class': 'form-select'
            }, choices=[
                ('bi-file-text', 'مستند نصي'),
                ('bi-file-pdf', 'ملف PDF'),
                ('bi-file-image', 'صورة'),
                ('bi-file-spreadsheet', 'جدول بيانات'),
                ('bi-file-word', 'مستند Word'),
                ('bi-folder', 'مجلد'),
                ('bi-archive', 'أرشيف'),
                ('bi-shield-check', 'مستند آمن'),
            ])
        }
        labels = {
            'name': 'اسم الفئة',
            'description': 'الوصف',
            'color': 'اللون',
            'icon': 'الأيقونة'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['description'].required = False


class DocumentVersionForm(forms.ModelForm):
    """Form for uploading new document versions"""
    
    class Meta:
        model = DocumentVersion
        fields = ['file', 'notes']
        widgets = {
            'file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx,.xls,.xlsx,.txt,.jpg,.jpeg,.png,.gif'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات حول هذا الإصدار'
            })
        }
        labels = {
            'file': 'الملف الجديد',
            'notes': 'ملاحظات الإصدار'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['notes'].required = False

    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            is_valid, message = validate_file(file)
            if not is_valid:
                raise ValidationError(message)
        return file


class DocumentSearchForm(forms.Form):
    """Form for searching documents"""
    
    search_query = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'ابحث في اسم المستند، الوصف، أو العلامات...'
        }),
        label='البحث'
    )
    
    document_type = forms.ChoiceField(
        required=False,
        choices=[('', 'جميع الأنواع')] + Document.DOCUMENT_TYPES,
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='نوع المستند'
    )
    
    category = forms.ModelChoiceField(
        required=False,
        queryset=DocumentCategory.objects.all(),
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='الفئة'
    )
    
    case = forms.ModelChoiceField(
        required=False,
        queryset=Case.objects.all(),
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='القضية'
    )
    
    client = forms.ModelChoiceField(
        required=False,
        queryset=Client.objects.all(),
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='العميل'
    )
    
    access_level = forms.ChoiceField(
        required=False,
        choices=[('', 'جميع المستويات')] + Document.ACCESS_LEVELS,
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='مستوى الوصول'
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='من تاريخ'
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='إلى تاريخ'
    )
    
    file_type = forms.ChoiceField(
        required=False,
        choices=[
            ('', 'جميع أنواع الملفات'),
            ('pdf', 'ملفات PDF'),
            ('image', 'الصور'),
            ('document', 'المستندات'),
            ('spreadsheet', 'جداول البيانات'),
        ],
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='نوع الملف'
    )
    
    sort_by = forms.ChoiceField(
        required=False,
        choices=[
            ('uploaded_at', 'تاريخ الرفع (الأحدث أولاً)'),
            ('-uploaded_at', 'تاريخ الرفع (الأقدم أولاً)'),
            ('name', 'الاسم (أ-ي)'),
            ('-name', 'الاسم (ي-أ)'),
            ('file_size', 'حجم الملف (الأصغر أولاً)'),
            ('-file_size', 'حجم الملف (الأكبر أولاً)'),
        ],
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='ترتيب النتائج'
    )

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_to < date_from:
            raise ValidationError('تاريخ النهاية يجب أن يكون بعد تاريخ البداية')
        
        return cleaned_data


class BulkDocumentActionForm(forms.Form):
    """Form for bulk actions on documents"""
    
    ACTION_CHOICES = [
        ('delete', 'حذف'),
        ('archive', 'أرشفة'),
        ('unarchive', 'إلغاء الأرشفة'),
        ('change_category', 'تغيير الفئة'),
        ('change_access_level', 'تغيير مستوى الوصول'),
    ]
    
    action = forms.ChoiceField(
        choices=ACTION_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='الإجراء'
    )
    
    category = forms.ModelChoiceField(
        required=False,
        queryset=DocumentCategory.objects.all(),
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='الفئة الجديدة'
    )
    
    access_level = forms.ChoiceField(
        required=False,
        choices=Document.ACCESS_LEVELS,
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='مستوى الوصول الجديد'
    )
    
    selected_documents = forms.CharField(
        widget=forms.HiddenInput(),
        label='المستندات المحددة'
    )

    def clean(self):
        cleaned_data = super().clean()
        action = cleaned_data.get('action')
        category = cleaned_data.get('category')
        access_level = cleaned_data.get('access_level')
        
        if action == 'change_category' and not category:
            raise ValidationError('يجب اختيار فئة عند تغيير الفئة')
        
        if action == 'change_access_level' and not access_level:
            raise ValidationError('يجب اختيار مستوى وصول عند تغيير مستوى الوصول')
        
        return cleaned_data
