from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.auth.decorators import permission_required
from .models import OfficeSettings
from .forms import OfficeSettingsForm

@permission_required('settings_app.change_officesettings')
def edit_settings(request):
    settings_obj, created = OfficeSettings.objects.get_or_create(pk=1)
    if request.method == 'POST':
        form = OfficeSettingsForm(request.POST, request.FILES, instance=settings_obj)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم حفظ الإعدادات بنجاح.')
            return redirect('settings_app:edit_settings')
    else:
        form = OfficeSettingsForm(instance=settings_obj)
    return render(request, 'settings_app/settings_form.html', {'form': form})
