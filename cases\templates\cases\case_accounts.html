{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-12 col-md-10 col-lg-8">
            <div class="card shadow rounded-4 border-0 p-4">
                <h4 class="mb-3 fw-bold"><i class="bi bi-cash-coin text-info me-2"></i>حسابات القضية: {{ case.title }}</h4>
                <div class="row mb-3">
                    <div class="col-md-4 mb-2">
                        <div class="alert alert-info text-center mb-0">إجمالي الأتعاب: <strong>{{ fees_total|floatformat:2 }}</strong> ج.م</div>
                    </div>
                    <div class="col-md-4 mb-2">
                        <div class="alert alert-success text-center mb-0">المدفوع: <strong>{{ paid|floatformat:2 }}</strong> ج.م</div>
                    </div>
                    <div class="col-md-4 mb-2">
                        <div class="alert alert-warning text-center mb-0">المتبقي: <strong>{{ unpaid|floatformat:2 }}</strong> ج.م</div>
                    </div>
                </div>
                <div class="mb-3 text-end">
                    <a href="{% url 'cases:add_case_payment' case.id %}" class="btn btn-success btn-sm px-4">
                        <i class="bi bi-plus-circle"></i> إضافة دفعة جديدة
                    </a>
                </div>
                <h5 class="fw-bold mt-4 mb-3"><i class="bi bi-cash-coin me-2"></i>جدول الأتعاب</h5>
                {% if fees %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover align-middle text-center">
                            <thead class="table-light">
                                <tr>
                                    <th>المبلغ</th>
                                    <th>النوع</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>تاريخ الدفع</th>
                                    <th>الحالة</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for fee in fees %}
                                <tr>
                                    <td>{{ fee.amount|floatformat:2 }}</td>
                                    <td>{{ fee.get_fee_type_display }}</td>
                                    <td>{{ fee.due_date|default:"-" }}</td>
                                    <td>{{ fee.paid_date|default:"-" }}</td>
                                    <td>
                                        {% if fee.is_paid %}
                                            <span class="badge bg-success">مدفوع</span>
                                        {% else %}
                                            <span class="badge bg-warning text-dark">غير مدفوع</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ fee.note|default:"-" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info text-center">لا توجد أتعاب مسجلة لهذه القضية بعد.</div>
                {% endif %}
                <h5 class="fw-bold mt-4 mb-3"><i class="bi bi-cash-stack me-2"></i>جدول الدفعات</h5>
                {% if payments %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover align-middle text-center">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>ملاحظات</th>
                                    <th>تفاصيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>{{ payment.date }}</td>
                                    <td>{{ payment.amount|floatformat:2 }}</td>
                                    <td>{{ payment.notes|default:'-' }}</td>
                                    <td>
                                        <a href="{% url 'clients:payment_detail' payment.id %}" class="btn btn-outline-info btn-sm"><i class="bi bi-eye"></i> تفاصيل</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info text-center">لا توجد دفعات مسجلة لهذه القضية بعد.</div>
                {% endif %}
                <div class="d-flex justify-content-end mt-3">
                    <a href="{% url 'cases:detail' case.id %}" class="btn btn-link"><i class="bi bi-arrow-right-circle"></i> العودة لتفاصيل القضية</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 