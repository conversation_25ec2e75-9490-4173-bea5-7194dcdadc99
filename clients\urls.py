from django.urls import path, include
from .views import (
    ClientListView, ClientCreateView, ClientUpdateView, ClientDetailView, ClientDeleteView, ClientViewSet, ClientAccountsReportView
)
from rest_framework.routers import Default<PERSON>outer
from . import views

app_name = 'clients'

router = DefaultRouter()
router.register(r'api/clients', ClientViewSet, basename='api-clients')

urlpatterns = [
    path('', ClientListView.as_view(), name='list'),
    path('add/', ClientCreateView.as_view(), name='add'),
    path('<int:pk>/edit/', ClientUpdateView.as_view(), name='edit'),
    path('<int:pk>/', ClientDetailView.as_view(), name='detail'),
    path('<int:pk>/delete/', ClientDeleteView.as_view(), name='delete'),
] + router.urls

urlpatterns += [
    path('<int:pk>/accounts_report/', ClientAccountsReportView.as_view(), name='accounts_report'),
    path('<int:client_id>/payments/', views.client_payments, name='client_payments'),
    path('<int:client_id>/payments/add/', views.add_payment, name='add_payment'),
    path('payments/<int:payment_id>/edit/', views.edit_payment, name='edit_payment'),
    path('payments/<int:payment_id>/delete/', views.delete_payment, name='delete_payment'),
    path('payments/<int:payment_id>/', views.payment_detail, name='payment_detail'),
] 