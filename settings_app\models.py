from django.db import models

# Create your models here.

class OfficeSettings(models.Model):
    office_name = models.CharField(max_length=255, verbose_name='اسم المكتب')
    logo = models.ImageField(upload_to='settings/', verbose_name='الشعار', blank=True, null=True)
    address = models.CharField(max_length=255, verbose_name='العنوان', blank=True, null=True)
    email = models.EmailField(verbose_name='البريد الإلكتروني', blank=True, null=True)
    phone = models.CharField(max_length=20, verbose_name='رقم الهاتف', blank=True, null=True)
    notes = models.TextField(verbose_name='ملاحظات', blank=True, null=True)
    # إعدادات متقدمة
    default_from_email = models.EmailField(verbose_name='البريد الافتراضي للإرسال', blank=True, null=True)
    enable_email_notifications = models.BooleanField(default=True, verbose_name='تفعيل إشعارات البريد الإلكتروني')
    enable_system_notifications = models.BooleanField(default=True, verbose_name='تفعيل إشعارات النظام')
    reports_page_size = models.PositiveIntegerField(default=20, verbose_name='عدد العناصر في صفحة التقارير')
    secondary_logo = models.ImageField(upload_to='settings/', verbose_name='شعار ثانوي', blank=True, null=True)
    custom_footer = models.CharField(max_length=255, verbose_name='نص تذييل مخصص', blank=True, null=True)

    def __str__(self):
        return self.office_name
