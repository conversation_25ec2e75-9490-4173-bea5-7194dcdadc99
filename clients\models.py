from django.db import models
from django.utils import timezone

# Create your models here.

class Client(models.Model):
    full_name = models.CharField(max_length=255, verbose_name='الاسم الكامل')
    national_id = models.CharField(max_length=50, verbose_name='رقم الهوية/جواز السفر')
    phone = models.CharField(max_length=20, verbose_name='رقم الهاتف')
    email = models.EmailField(verbose_name='البريد الإلكتروني', blank=True, null=True)
    address = models.CharField(max_length=255, verbose_name='العنوان', blank=True, null=True)
    notes = models.TextField(verbose_name='ملاحظات', blank=True, null=True)
    
    def __str__(self):
        return self.full_name

class Payment(models.Model):
    client = models.ForeignKey('Client', on_delete=models.CASCADE, related_name='payments', verbose_name='العميل')
    case = models.ForeignKey('cases.Case', on_delete=models.SET_NULL, null=True, blank=True, related_name='payments', verbose_name='القضية')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='المبلغ')
    date = models.DateField(default=timezone.now, verbose_name='تاريخ الدفعة')
    notes = models.CharField(max_length=255, blank=True, verbose_name='ملاحظات')

    class Meta:
        verbose_name = 'دفعة'
        verbose_name_plural = 'الدفعات'
        ordering = ['-date']

    def __str__(self):
        return f"دفعة {self.amount} للعميل {self.client} بتاريخ {self.date}"
