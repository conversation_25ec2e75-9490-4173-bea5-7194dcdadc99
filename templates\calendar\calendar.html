{% extends 'base.html' %}
{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="fw-bolder">
            <i class="bi bi-calendar-event text-primary me-2"></i>
            التقويم العام للمواعيد والجلسات
        </h3>
        <a href="{% url 'appointments:add' %}" class="btn btn-primary">
            <i class="bi bi-plus-lg me-1"></i> إنشاء موعد جديد
        </a>
    </div>
    <div class="card border-0 shadow-sm overflow-hidden animate__animated animate__fadeIn">
        <div id="calendar"></div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.11/index.global.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
<style>
#calendar {
    background: transparent;
    padding: 1rem;
}
.fc-daygrid-event {
    border-radius: 0.5rem;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}
.fc-daygrid-event:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}
.fc-toolbar-title {
    font-size: 1.2rem;
    font-weight: bold;
}
.fc-button {
    border-radius: 0.5rem !important;
    padding: 0.3rem 0.8rem !important;
}
.fc-col-header-cell-cushion {
    font-weight: bold;
}
.fc-daygrid-day-frame {
    transition: background 0.2s;
}
.fc-daygrid-day-frame:hover {
    background: #f8f9fa;
}
</style>
{% endblock %}
{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.11/index.global.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    var calendarEl = document.getElementById('calendar');
    var calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        locale: 'ar',
        height: 650,
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay'
        },
        events: '/calendar/events/',
        eventClick: function(info) {
            if (info.event.url) {
                window.location.href = info.event.url;
                info.jsEvent.preventDefault();
            }
        },
    });
    calendar.render();
});
</script>
{% endblock %}
