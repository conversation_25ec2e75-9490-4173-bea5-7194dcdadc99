{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10">
            <div class="card shadow rounded-4 border-0">
                <div class="card-body p-4">
                    <h3 class="mb-4 text-center fw-bold">
                        <i class="bi bi-person-badge text-primary me-2"></i>
                        تقرير حسابات العميل: <span class="text-primary">{{ client.full_name }}</span>
                    </h3>
                    <div class="row mb-4">
                        <div class="col-md-4 mb-2">
                            <div class="alert alert-success text-center mb-0">المدفوع: <strong>{{ total_paid|floatformat:2 }}</strong> ج.م</div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="alert alert-warning text-center mb-0">المتبقي: <strong>{{ total_unpaid|floatformat:2 }}</strong> ج.م</div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="alert alert-info text-center mb-0">إجمالي الأتعاب: <strong>{{ total_fees|floatformat:2 }}</strong> ج.م</div>
                        </div>
                    </div>
                    <h5 class="fw-bold mt-4 mb-3"><i class="bi bi-briefcase me-2"></i>تفاصيل القضايا والأتعاب</h5>
                    {% if case_fees %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover align-middle text-center">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم القضية</th>
                                        <th>عنوان القضية</th>
                                        <th>إجمالي الأتعاب</th>
                                        <th>المدفوع</th>
                                        <th>المتبقي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in case_fees %}
                                    <tr>
                                        <td>{{ item.case.case_number }}</td>
                                        <td>{{ item.case.title }}</td>
                                        <td>{{ item.total|floatformat:2 }}</td>
                                        <td>{{ item.paid|floatformat:2 }}</td>
                                        <td>{{ item.unpaid|floatformat:2 }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info text-center">لا توجد قضايا مسجلة لهذا العميل.</div>
                    {% endif %}
                    <div class="mt-4 text-end">
                        <a href="{% url 'clients:detail' client.pk %}" class="btn btn-link">
                            <i class="bi bi-arrow-right-circle"></i> العودة لتفاصيل العميل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
{% endblock %} 