/* Custom CSS for Lawyers Office System */

/* Root Variables for Consistent Theming */
:root {
    --primary-color: #222b45;
    --secondary-color: #ffd600;
    --accent-color: #ffb300;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 0.75rem;
    --box-shadow: 0 4px 20px rgba(34, 43, 69, 0.08);
    --transition: all 0.3s ease;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e6ea 100%);
    min-height: 100vh;
}

/* Enhanced Sidebar */
.sidebar {
    background: linear-gradient(135deg, var(--primary-color) 0%, #2b365e 100%);
    box-shadow: 2px 0 20px rgba(34, 43, 69, 0.15);
    transition: var(--transition);
    z-index: 1040;
}

.sidebar .nav-link {
    padding: 0.75rem 1.25rem;
    margin: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 214, 0, 0.1), transparent);
    transition: var(--transition);
}

.sidebar .nav-link:hover::before {
    left: 100%;
}

.sidebar .nav-link.active {
    background: linear-gradient(90deg, var(--secondary-color) 0%, var(--accent-color) 100%);
    color: var(--primary-color);
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(255, 214, 0, 0.3);
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(34, 43, 69, 0.12);
}

/* Enhanced Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.5rem 1.25rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-gradient-gold {
    background: linear-gradient(90deg, var(--secondary-color) 0%, var(--accent-color) 100%);
    color: var(--primary-color);
    border: none;
    font-weight: 600;
}

.btn-gradient-gold:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(255, 214, 0, 0.4);
    color: var(--primary-color);
}

.btn-gradient-primary {
    background: linear-gradient(90deg, var(--primary-color) 0%, #3d4a6b 100%);
    color: white;
    border: none;
}

.btn-gradient-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(34, 43, 69, 0.3);
    color: white;
}

/* Enhanced Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(90deg, var(--primary-color) 0%, #3d4a6b 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(255, 214, 0, 0.05);
    transform: scale(1.01);
}

/* Enhanced Forms */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 214, 0, 0.25);
}

/* Dashboard Cards */
.dashboard-link-card {
    transition: var(--transition);
    text-decoration: none;
    color: inherit;
    position: relative;
    overflow: hidden;
}

.dashboard-link-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.dashboard-link-card:hover::before {
    left: 100%;
}

.dashboard-link-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: inherit;
}

/* Mobile Responsive Enhancements */
@media (max-width: 991px) {
    .sidebar {
        position: fixed;
        top: 0;
        right: -100%;
        width: 280px;
        height: 100vh;
        transition: right 0.3s ease;
        z-index: 1050;
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .content-wrapper {
        padding: 1rem;
    }
}

@media (max-width: 575px) {
    .dashboard-link-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 214, 0, 0.3);
    border-radius: 50%;
    border-top-color: var(--secondary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Notification Enhancements */
.notification-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Dark Theme Support */
[data-theme='dark'] {
    --light-color: #2c3e50;
    --dark-color: #ecf0f1;
}

[data-theme='dark'] body {
    background: linear-gradient(135deg, #232526 0%, #414345 100%);
    color: var(--dark-color);
}

[data-theme='dark'] .card {
    background-color: #34495e;
    color: var(--dark-color);
}

[data-theme='dark'] .table {
    background-color: #34495e;
    color: var(--dark-color);
}

[data-theme='dark'] .form-control,
[data-theme='dark'] .form-select {
    background-color: #34495e;
    border-color: #4a5f7a;
    color: var(--dark-color);
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        width: 100%;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
