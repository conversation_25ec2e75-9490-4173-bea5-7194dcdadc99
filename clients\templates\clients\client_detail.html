{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-md-7 col-lg-6">
            <div class="card shadow rounded-4 border-0 p-4">
                <h4 class="mb-3 fw-bold"><i class="bi bi-person text-primary me-2"></i>تفاصيل العميل</h4>
                <div class="d-flex justify-content-end mb-3">
                    <a href="{% url 'clients:accounts_report' client.pk %}" class="btn btn-info btn-sm px-4">
                        <i class="bi bi-cash-coin"></i> تقرير الحسابات
                    </a>
                    <a href="{% url 'clients:client_payments' client.id %}" class="btn btn-success btn-sm px-4 ms-2">
                        <i class="bi bi-cash-stack"></i> دفعات العميل
                    </a>
                </div>
                <ul class="list-group list-group-flush mb-3">
                    <li class="list-group-item">الاسم الكامل: <strong>{{ client.full_name }}</strong></li>
                    <li class="list-group-item">رقم الهوية: <strong>{{ client.national_id }}</strong></li>
                    <li class="list-group-item">رقم الهاتف: <strong>{{ client.phone }}</strong></li>
                    <li class="list-group-item">البريد الإلكتروني: <strong>{{ client.email }}</strong></li>
                    <li class="list-group-item">العنوان: <strong>{{ client.address }}</strong></li>
                    <li class="list-group-item">ملاحظات: <strong>{{ client.notes }}</strong></li>
                </ul>
                <div class="d-flex justify-content-between gap-2">
                    <a href="{% url 'clients:edit' client.pk %}" class="btn btn-outline-primary flex-fill"><i class="bi bi-pencil-square"></i> تعديل</a>
                    <a href="{% url 'clients:delete' client.pk %}" class="btn btn-outline-danger flex-fill"><i class="bi bi-trash"></i> حذف</a>
                    <a href="{% url 'clients:list' %}" class="btn btn-link flex-fill"><i class="bi bi-arrow-right-circle"></i> العودة للقائمة</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
{% endblock %} 