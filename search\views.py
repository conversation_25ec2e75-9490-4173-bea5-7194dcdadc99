from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.db.models import Q, Count
from django.core.paginator import Paginator
from django.utils import timezone
from datetime import datetime, timedelta
import json

from cases.models import Case
from clients.models import Client
from documents.models import Document
from appointments.models import Appointment
from users.models import User

ENTITY_MODELS = {
    'cases': Case,
    'clients': Client,
    'documents': Document,
    'appointments': Appointment,
}

ENTITY_LABELS = {
    'cases': 'القضايا',
    'clients': 'العملاء',
    'documents': 'المستندات',
    'appointments': 'المواعيد',
}

ENTITY_ICONS = {
    'cases': 'bi-briefcase',
    'clients': 'bi-people',
    'documents': 'bi-file-earmark-text',
    'appointments': 'bi-calendar-event',
}

@login_required
def advanced_search(request):
    """Enhanced advanced search with comprehensive filtering"""
    query = request.GET.get('q', '').strip()
    entity_type = request.GET.get('type', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    case_status = request.GET.get('case_status', '')
    sort_by = request.GET.get('sort', 'relevance')

    results = []
    total_count = 0

    if query or entity_type or date_from or date_to or case_status:
        if entity_type and entity_type in ENTITY_MODELS:
            # Search specific entity type
            results = search_entity(entity_type, query, request.GET)
            total_count = len(results)
        else:
            # Search all entities
            all_results = {}
            for entity, model in ENTITY_MODELS.items():
                entity_results = search_entity(entity, query, request.GET)
                if entity_results:
                    all_results[entity] = {
                        'results': entity_results[:5],  # Limit to 5 per type for overview
                        'count': len(entity_results),
                        'label': ENTITY_LABELS[entity],
                        'icon': ENTITY_ICONS[entity]
                    }
                    total_count += len(entity_results)

            results = all_results

    # Get search suggestions
    suggestions = get_search_suggestions(query) if query else []

    context = {
        'query': query,
        'entity_type': entity_type,
        'date_from': date_from,
        'date_to': date_to,
        'case_status': case_status,
        'sort_by': sort_by,
        'results': results,
        'total_count': total_count,
        'entity_labels': ENTITY_LABELS,
        'entity_icons': ENTITY_ICONS,
        'suggestions': suggestions,
        'has_results': bool(results),
    }

    return render(request, 'search/advanced_search.html', context)

def search_entity(entity_type, query, filters):
    """Search within a specific entity type"""
    if entity_type not in ENTITY_MODELS:
        return []

    model = ENTITY_MODELS[entity_type]
    queryset = model.objects.all()

    # Apply text search
    if query:
        if entity_type == 'cases':
            queryset = queryset.filter(
                Q(case_number__icontains=query) |
                Q(title__icontains=query) |
                Q(description__icontains=query) |
                Q(client__full_name__icontains=query)
            ).select_related('client', 'lawyer')

        elif entity_type == 'clients':
            queryset = queryset.filter(
                Q(full_name__icontains=query) |
                Q(national_id__icontains=query) |
                Q(phone__icontains=query) |
                Q(email__icontains=query)
            )

        elif entity_type == 'documents':
            queryset = queryset.filter(
                Q(name__icontains=query) |
                Q(description__icontains=query) |
                Q(tags__icontains=query) |
                Q(case__title__icontains=query) |
                Q(client__full_name__icontains=query)
            ).select_related('case', 'client', 'uploaded_by')

        elif entity_type == 'appointments':
            queryset = queryset.filter(
                Q(title__icontains=query) |
                Q(notes__icontains=query) |
                Q(client__full_name__icontains=query) |
                Q(location__icontains=query)
            ).select_related('client', 'lawyer', 'case')

    # Apply date filters
    date_from = filters.get('date_from')
    date_to = filters.get('date_to')

    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            if entity_type == 'cases':
                queryset = queryset.filter(start_date__gte=date_from)
            elif entity_type == 'documents':
                queryset = queryset.filter(uploaded_at__date__gte=date_from)
            elif entity_type == 'appointments':
                queryset = queryset.filter(datetime__date__gte=date_from)
            elif entity_type == 'clients':
                queryset = queryset.filter(id__in=Case.objects.filter(
                    start_date__gte=date_from
                ).values_list('client_id', flat=True))
        except ValueError:
            pass

    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
            if entity_type == 'cases':
                queryset = queryset.filter(start_date__lte=date_to)
            elif entity_type == 'documents':
                queryset = queryset.filter(uploaded_at__date__lte=date_to)
            elif entity_type == 'appointments':
                queryset = queryset.filter(datetime__date__lte=date_to)
            elif entity_type == 'clients':
                queryset = queryset.filter(id__in=Case.objects.filter(
                    start_date__lte=date_to
                ).values_list('client_id', flat=True))
        except ValueError:
            pass

    # Apply case status filter
    case_status = filters.get('case_status')
    if case_status and entity_type == 'cases':
        queryset = queryset.filter(status=case_status)

    # Apply sorting
    sort_by = filters.get('sort', 'relevance')
    if sort_by == 'date_desc':
        if entity_type == 'cases':
            queryset = queryset.order_by('-start_date')
        elif entity_type == 'documents':
            queryset = queryset.order_by('-uploaded_at')
        elif entity_type == 'appointments':
            queryset = queryset.order_by('-datetime')
        elif entity_type == 'clients':
            queryset = queryset.order_by('-id')
    elif sort_by == 'date_asc':
        if entity_type == 'cases':
            queryset = queryset.order_by('start_date')
        elif entity_type == 'documents':
            queryset = queryset.order_by('uploaded_at')
        elif entity_type == 'appointments':
            queryset = queryset.order_by('datetime')
        elif entity_type == 'clients':
            queryset = queryset.order_by('id')
    elif sort_by == 'name_asc':
        if entity_type == 'cases':
            queryset = queryset.order_by('title')
        elif entity_type == 'documents':
            queryset = queryset.order_by('name')
        elif entity_type == 'appointments':
            queryset = queryset.order_by('title')
        elif entity_type == 'clients':
            queryset = queryset.order_by('full_name')
    elif sort_by == 'name_desc':
        if entity_type == 'cases':
            queryset = queryset.order_by('-title')
        elif entity_type == 'documents':
            queryset = queryset.order_by('-name')
        elif entity_type == 'appointments':
            queryset = queryset.order_by('-title')
        elif entity_type == 'clients':
            queryset = queryset.order_by('-full_name')

    return list(queryset[:50])  # Limit results

def get_search_suggestions(query):
    """Get search suggestions based on query"""
    suggestions = []

    if len(query) >= 2:
        # Case suggestions
        cases = Case.objects.filter(
            Q(case_number__icontains=query) | Q(title__icontains=query)
        )[:5]
        for case in cases:
            suggestions.append({
                'text': case.case_number,
                'type': 'case',
                'label': f"القضية: {case.case_number}"
            })

        # Client suggestions
        clients = Client.objects.filter(
            Q(full_name__icontains=query) | Q(national_id__icontains=query)
        )[:5]
        for client in clients:
            suggestions.append({
                'text': client.full_name,
                'type': 'client',
                'label': f"العميل: {client.full_name}"
            })

    return suggestions

@login_required
def search_api(request):
    """API endpoint for live search"""
    query = request.GET.get('q', '').strip()
    entity_type = request.GET.get('type', '')
    limit = int(request.GET.get('limit', 10))

    if len(query) < 2:
        return JsonResponse({'results': []})

    results = []

    if entity_type and entity_type in ENTITY_MODELS:
        # Search specific entity
        entity_results = search_entity(entity_type, query, request.GET)[:limit]
        for item in entity_results:
            results.append(format_search_result(item, entity_type))
    else:
        # Search all entities
        for entity, model in ENTITY_MODELS.items():
            entity_results = search_entity(entity, query, request.GET)[:3]
            for item in entity_results:
                results.append(format_search_result(item, entity))

    return JsonResponse({'results': results[:limit]})

def format_search_result(item, entity_type):
    """Format search result for API response"""
    if entity_type == 'cases':
        return {
            'id': item.id,
            'title': item.title,
            'subtitle': f"رقم القضية: {item.case_number}",
            'description': item.description[:100] if item.description else '',
            'url': f"/cases/{item.id}/",
            'type': 'case',
            'icon': 'bi-briefcase',
            'meta': f"العميل: {item.client.full_name}"
        }
    elif entity_type == 'clients':
        return {
            'id': item.id,
            'title': item.full_name,
            'subtitle': f"رقم الهوية: {item.national_id}",
            'description': item.phone,
            'url': f"/clients/{item.id}/",
            'type': 'client',
            'icon': 'bi-person',
            'meta': item.email or ''
        }
    elif entity_type == 'documents':
        return {
            'id': item.id,
            'title': item.name,
            'subtitle': f"نوع: {item.get_document_type_display()}",
            'description': item.description[:100] if item.description else '',
            'url': f"/documents/{item.id}/",
            'type': 'document',
            'icon': 'bi-file-earmark-text',
            'meta': f"تاريخ الرفع: {item.uploaded_at.strftime('%Y-%m-%d')}"
        }
    elif entity_type == 'appointments':
        return {
            'id': item.id,
            'title': item.title,
            'subtitle': f"مع: {item.client.full_name}",
            'description': item.notes[:100] if item.notes else '',
            'url': f"/appointments/{item.id}/",
            'type': 'appointment',
            'icon': 'bi-calendar-event',
            'meta': f"التاريخ: {item.datetime.strftime('%Y-%m-%d %H:%M')}"
        }

    return {}
