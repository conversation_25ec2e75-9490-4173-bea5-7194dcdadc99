from django.shortcuts import render
from cases.models import Case
from clients.models import Client
from documents.models import Document
from appointments.models import Appointment

ENTITY_MODELS = {
    'case': Case,
    'client': Client,
    'document': Document,
    'appointment': Appointment,
}

ENTITY_LABELS = {
    'case': 'قضية',
    'client': 'عميل',
    'document': 'مستند',
    'appointment': 'موعد',
}

def advanced_search(request):
    entity = request.GET.get('entity')
    results = None
    filters = {}
    if entity in ENTITY_MODELS:
        Model = ENTITY_MODELS[entity]
        # فلاتر بسيطة حسب الكيان
        if entity == 'case':
            filters = {
                'case_number': request.GET.get('case_number', ''),
                'title': request.GET.get('title', ''),
                'status': request.GET.get('status', ''),
            }
            qs = Model.objects.all()
            if filters['case_number']:
                qs = qs.filter(case_number__icontains=filters['case_number'])
            if filters['title']:
                qs = qs.filter(title__icontains=filters['title'])
            if filters['status']:
                qs = qs.filter(status=filters['status'])
            results = qs
        elif entity == 'client':
            filters = {
                'full_name': request.GET.get('full_name', ''),
                'national_id': request.GET.get('national_id', ''),
            }
            qs = Model.objects.all()
            if filters['full_name']:
                qs = qs.filter(full_name__icontains=filters['full_name'])
            if filters['national_id']:
                qs = qs.filter(national_id__icontains=filters['national_id'])
            results = qs
        elif entity == 'document':
            filters = {
                'name': request.GET.get('name', ''),
            }
            qs = Model.objects.all()
            if filters['name']:
                qs = qs.filter(name__icontains=filters['name'])
            results = qs
        elif entity == 'appointment':
            filters = {
                'title': request.GET.get('title', ''),
            }
            qs = Model.objects.all()
            if filters['title']:
                qs = qs.filter(title__icontains=filters['title'])
            results = qs
    context = {
        'entity': entity,
        'entity_labels': ENTITY_LABELS,
        'filters': filters,
        'results': results,
    }
    return render(request, 'search/advanced_search.html', context)
