{% load static %}
<nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
    <div class="container-fluid">
        <a class="navbar-brand fw-bold" href="/">مكتب المحاماة</a>
        <div class="d-flex align-items-center ms-auto">
            <!-- أيقونة الوضع الليلي/النهاري -->
            <span class="theme-toggle me-3" onclick="toggleTheme()" title="تبديل الوضع الليلي/النهاري">
                <svg id="themeIcon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-moon" viewBox="0 0 16 16">
                    <path d="M6 0a7 7 0 0 0 0 14c3.866 0 7-3.134 7-7 0-.256-.012-.51-.035-.762A6.978 6.978 0 0 1 6 0z"/>
                </svg>
            </span>
            <!-- أيقونة الإشعارات -->
            <div class="dropdown">
                <a class="btn position-relative" href="#" id="notificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-bell" viewBox="0 0 16 16">
                        <path d="M8 16a2 2 0 0 0 2-2H6a2 2 0 0 0 2 2zm.104-14.804A1 1 0 0 0 7 2c0 .628-.134 1.197-.356 1.684C5.21 4.09 4 5.388 4 7v3.586l-.707.707A1 1 0 0 0 4 13h8a1 1 0 0 0 .707-1.707L12 10.586V7c0-1.612-1.21-2.91-2.644-3.316A3.001 3.001 0 0 0 8.104 1.196z"/>
                    </svg>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notif-count">
                        {{ notifications_unread_count|default:0 }}
                    </span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown" style="min-width: 300px; max-height: 350px; overflow-y: auto;">
                    {% if notifications %}
                        {% for notif in notifications|slice:":5" %}
                        <li class="dropdown-item small {% if not notif.is_read %}fw-bold{% endif %}">
                            <a href="{% url 'notifications:mark_as_read_and_redirect' notif.pk %}" class="text-decoration-none d-block">
                                <div>{{ notif.title }}</div>
                                <small class="text-muted">{{ notif.created_at|date:"Y-m-d H:i" }}</small>
                            </a>
                        </li>
                        {% endfor %}
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center" href="{% url 'notifications:list' %}">عرض كل الإشعارات</a></li>
                    {% else %}
                        <li class="dropdown-item text-center text-muted">لا توجد إشعارات</li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>
</nav>
<script>
    function toggleTheme() {
        const body = document.body;
        const icon = document.getElementById('themeIcon');
        if (body.getAttribute('data-theme') === 'dark') {
            body.setAttribute('data-theme', 'light');
            icon.innerHTML = '<path d="M6 0a7 7 0 0 0 0 14c3.866 0 7-3.134 7-7 0-.256-.012-.51-.035-.762A6.978 6.978 0 0 1 6 0z"/>';
        } else {
            body.setAttribute('data-theme', 'dark');
            icon.innerHTML = '<path d="M12.293 9.293a1 1 0 0 1 1.414 1.414A7 7 0 1 1 6 0a1 1 0 0 1 0 2 5 5 0 1 0 5 5 1 1 0 0 1 1.293 2.293z"/>';
        }
    }
</script> 