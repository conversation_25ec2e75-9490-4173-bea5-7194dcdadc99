{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <h3 class="mb-4 fw-bold"><i class="bi bi-calendar-event text-primary me-2"></i>تقرير المواعيد</h3>
    <form method="get" class="row g-3 mb-4 align-items-end">
        <div class="col-md-3">
            <label class="form-label">المحامي</label>
            <select name="lawyer" class="form-select">
                <option value="">كل المحامين</option>
                {% for lawyer in lawyers %}
                    <option value="{{ lawyer.id }}" {% if lawyer.id|stringformat:'s' == selected_lawyer %}selected{% endif %}>{{ lawyer.get_full_name|default:lawyer.username }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">القضية</label>
            <select name="case" class="form-select">
                <option value="">كل القضايا</option>
                {% for case in cases %}
                    <option value="{{ case.id }}" {% if case.id|stringformat:'s' == selected_case %}selected{% endif %}>{{ case.title }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">التاريخ</label>
            <input type="date" name="date" class="form-control" value="{{ selected_date }}">
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-primary w-100">تصفية</button>
        </div>
    </form>
    <div class="mb-3 d-flex flex-wrap gap-2">
        <a href="{% url 'reports:appointments_report_pdf' %}?lawyer={{ selected_lawyer }}&case={{ selected_case }}&date={{ selected_date }}" class="btn btn-outline-danger">تصدير PDF</a>
        <a href="{% url 'reports:appointments_report_excel' %}?lawyer={{ selected_lawyer }}&case={{ selected_case }}&date={{ selected_date }}" class="btn btn-outline-success">تصدير Excel</a>
    </div>
    <div class="mb-4">
        <canvas id="appointmentsChart" height="100"></canvas>
    </div>
    <div class="card p-3 shadow rounded-4 border-0">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>العنوان</th>
                        <th>القضية</th>
                        <th>العميل</th>
                        <th>المحامي</th>
                        <th>التاريخ والوقت</th>
                        <th>الموقع</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for appointment in appointments %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ appointment.title }}</td>
                        <td>{{ appointment.case }}</td>
                        <td>{{ appointment.client }}</td>
                        <td>{{ appointment.lawyer }}</td>
                        <td>{{ appointment.datetime|date:'Y-m-d H:i' }}</td>
                        <td>{{ appointment.location }}</td>
                        <td>{{ appointment.notes }}</td>
                    </tr>
                    {% empty %}
                    <tr><td colspan="8" class="text-center">لا توجد مواعيد مطابقة.</td></tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
{% endblock %}
{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // رسم بياني Chart.js
    const chartData = JSON.parse('{{ chart_data|safe|escapejs }}');
    const ctx = document.getElementById('appointmentsChart').getContext('2d');
    const labels = chartData.map(item => item.label);
    const data = chartData.map(item => item.count);
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'عدد المواعيد لكل محامي',
                data: data,
                backgroundColor: '#0d6efd',
            }]
        },
        options: {
            plugins: {
                legend: { display: false }
            },
            scales: {
                y: { beginAtZero: true }
            }
        }
    });
</script>
{% endblock %} 