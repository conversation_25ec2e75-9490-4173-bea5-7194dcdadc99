from django.shortcuts import render, get_object_or_404
from django.urls import reverse_lazy, reverse
from django.views.generic import ListView, CreateView, UpdateView, DetailView, DeleteView
from .models import Case, Hearing
from notifications.utils import create_notification
from django.contrib.auth.mixins import PermissionRequiredMixin
from activitylog.utils import log_activity
from rest_framework import viewsets, permissions
from .serializers import CaseSerializer
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from django.core.mail import send_mail
from .models import HearingDocument, CourtDecision, HearingAction, HearingImportantDate, HearingReminder
from django import forms
from django.contrib.auth.mixins import LoginRequiredMixin
from django.shortcuts import redirect
from django.http import Http404
from .models import Fee
from clients.models import Payment
from clients.forms import PaymentForm
from django.db import models
from django.contrib import messages

# Create your views here.

class CaseListView(ListView):
    model = Case
    template_name = 'cases/case_list.html'
    context_object_name = 'cases'

class CaseCreateView(PermissionRequiredMixin, CreateView):
    permission_required = 'cases.add_case'
    model = Case
    fields = ['case_number', 'title', 'description', 'client', 'lawyer', 'start_date', 'end_date', 'status']
    template_name = 'cases/case_form.html'
    success_url = reverse_lazy('cases:list')

    def form_valid(self, form):
        response = super().form_valid(form)
        case = self.object
        # إشعار للعميل عبر البريد الإلكتروني فقط
        if case.client and case.client.email:
            send_mail(
                subject='تم إضافة قضية جديدة',
                message=f'تم إضافة القضية "{case.title}".',
                from_email=None,  # سيستخدم DEFAULT_FROM_EMAIL من الإعدادات
                recipient_list=[case.client.email],
                fail_silently=True,
            )
        # إشعار للمحامي
        if case.lawyer:
            create_notification(
                user=case.lawyer,
                title='تم تعيينك على قضية جديدة',
                message=f'تم تعيينك على القضية "{case.title}".',
                url=reverse('cases:detail', args=[case.pk]),
                notification_type='case',
                send_email=True
            )
        log_activity(self.request.user, 'case', case.pk, 'create', f'إضافة قضية: {case.title}')
        return response

class CaseUpdateView(PermissionRequiredMixin, UpdateView):
    permission_required = 'cases.change_case'
    model = Case
    fields = ['case_number', 'title', 'description', 'client', 'lawyer', 'start_date', 'end_date', 'status']
    template_name = 'cases/case_form.html'
    success_url = reverse_lazy('cases:list')

    def form_valid(self, form):
        response = super().form_valid(form)
        case = self.object
        # إشعار للعميل عبر البريد الإلكتروني فقط
        if case.client and case.client.email:
            send_mail(
                subject='تم تعديل قضية',
                message=f'تم تعديل بيانات القضية "{case.title}".',
                from_email=None,  # سيستخدم DEFAULT_FROM_EMAIL من الإعدادات
                recipient_list=[case.client.email],
                fail_silently=True,
            )
        # إشعار للمحامي
        if case.lawyer:
            create_notification(
                user=case.lawyer,
                title='تم تعديل قضية تخصك',
                message=f'تم تعديل بيانات القضية "{case.title}".',
                url=reverse('cases:detail', args=[case.pk]),
                notification_type='case',
                send_email=True
            )
        log_activity(self.request.user, 'case', case.pk, 'update', f'تعديل قضية: {case.title}')
        return response

class CaseDetailView(DetailView):
    model = Case
    template_name = 'cases/case_detail.html'
    context_object_name = 'case'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearings'] = self.object.hearings.all().order_by('-date')
        # أتعاب القضية
        fees = self.object.fees.all().order_by('-due_date')
        context['fees'] = fees
        context['fees_paid'] = sum(f.amount for f in fees if f.is_paid)
        context['fees_total'] = sum(f.amount for f in fees)
        context['fees_unpaid'] = context['fees_total'] - context['fees_paid']
        context['payments'] = self.object.payments.all()
        return context

class CaseDeleteView(PermissionRequiredMixin, DeleteView):
    permission_required = 'cases.delete_case'
    model = Case
    template_name = 'cases/case_confirm_delete.html'
    success_url = reverse_lazy('cases:list')

    def delete(self, request, *args, **kwargs):
        case = self.get_object()
        log_activity(request.user, 'case', case.pk, 'delete', f'حذف قضية: {case.title}')
        return super().delete(request, *args, **kwargs)

class HearingCreateView(LoginRequiredMixin, CreateView):
    model = Hearing
    fields = ['date', 'description', 'location']
    template_name = 'cases/hearing_form.html'

    def dispatch(self, request, *args, **kwargs):
        self.case = Case.objects.get(pk=kwargs['case_pk'])
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        form.instance.case = self.case
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('cases:detail', args=[self.case.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['case'] = self.case
        return context

class HearingDetailView(DetailView):
    model = Hearing
    template_name = 'cases/hearing_detail.html'
    context_object_name = 'hearing'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['documents'] = self.object.documents.all()
        context['court_decision'] = getattr(self.object, 'court_decision', None)
        context['actions'] = self.object.actions.all()
        context['important_dates'] = self.object.important_dates.all()
        context['reminders'] = self.object.reminders.all()
        return context

class HearingDocumentCreateView(LoginRequiredMixin, CreateView):
    model = HearingDocument
    fields = ['file', 'description']
    template_name = 'cases/hearingdocument_form.html'

    def dispatch(self, request, *args, **kwargs):
        self.hearing = Hearing.objects.get(pk=kwargs['hearing_pk'])
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        form.instance.hearing = self.hearing
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.hearing
        return context

class HearingDocumentUpdateView(LoginRequiredMixin, UpdateView):
    model = HearingDocument
    fields = ['file', 'description']
    template_name = 'cases/hearingdocument_form.html'

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.object.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.object.hearing
        context['is_update'] = True
        return context

class HearingDocumentDeleteView(LoginRequiredMixin, DeleteView):
    model = HearingDocument
    template_name = 'cases/hearingdocument_confirm_delete.html'

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.object.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.object.hearing
        return context

class HearingActionCreateView(LoginRequiredMixin, CreateView):
    model = HearingAction
    fields = ['action', 'is_completed', 'due_date']
    template_name = 'cases/hearingaction_form.html'

    def dispatch(self, request, *args, **kwargs):
        self.hearing = Hearing.objects.get(pk=kwargs['hearing_pk'])
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        form.instance.hearing = self.hearing
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.hearing
        return context

class HearingActionUpdateView(LoginRequiredMixin, UpdateView):
    model = HearingAction
    fields = ['action', 'is_completed', 'due_date']
    template_name = 'cases/hearingaction_form.html'

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.object.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.object.hearing
        context['is_update'] = True
        return context

class HearingActionDeleteView(LoginRequiredMixin, DeleteView):
    model = HearingAction
    template_name = 'cases/hearingaction_confirm_delete.html'

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.object.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.object.hearing
        return context

class HearingImportantDateCreateView(LoginRequiredMixin, CreateView):
    model = HearingImportantDate
    fields = ['date', 'description']
    template_name = 'cases/hearingimportantdate_form.html'

    def dispatch(self, request, *args, **kwargs):
        self.hearing = Hearing.objects.get(pk=kwargs['hearing_pk'])
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        form.instance.hearing = self.hearing
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.hearing
        return context

class HearingImportantDateUpdateView(LoginRequiredMixin, UpdateView):
    model = HearingImportantDate
    fields = ['date', 'description']
    template_name = 'cases/hearingimportantdate_form.html'

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.object.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.object.hearing
        context['is_update'] = True
        return context

class HearingImportantDateDeleteView(LoginRequiredMixin, DeleteView):
    model = HearingImportantDate
    template_name = 'cases/hearingimportantdate_confirm_delete.html'

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.object.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.object.hearing
        return context

class HearingReminderCreateView(LoginRequiredMixin, CreateView):
    model = HearingReminder
    fields = ['reminder_date', 'note']
    template_name = 'cases/hearingreminder_form.html'

    def dispatch(self, request, *args, **kwargs):
        self.hearing = Hearing.objects.get(pk=kwargs['hearing_pk'])
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        form.instance.hearing = self.hearing
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.hearing
        return context

class HearingReminderUpdateView(LoginRequiredMixin, UpdateView):
    model = HearingReminder
    fields = ['reminder_date', 'note']
    template_name = 'cases/hearingreminder_form.html'

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.object.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.object.hearing
        context['is_update'] = True
        return context

class HearingReminderDeleteView(LoginRequiredMixin, DeleteView):
    model = HearingReminder
    template_name = 'cases/hearingreminder_confirm_delete.html'

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.object.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.object.hearing
        return context

class CourtDecisionCreateUpdateView(LoginRequiredMixin, UpdateView):
    model = CourtDecision
    fields = ['text', 'file', 'date_issued']
    template_name = 'cases/courtdecision_form.html'

    def dispatch(self, request, *args, **kwargs):
        self.hearing = Hearing.objects.get(pk=kwargs['hearing_pk'])
        try:
            self.object = self.hearing.court_decision
            self.is_update = True
        except CourtDecision.DoesNotExist:
            self.object = None
            self.is_update = False
        if self.is_update:
            self.kwargs['pk'] = self.object.pk
        return super().dispatch(request, *args, **kwargs)

    def get_object(self, queryset=None):
        if self.is_update:
            return self.object
        return None

    def form_valid(self, form):
        form.instance.hearing = self.hearing
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.hearing
        context['is_update'] = self.is_update
        return context

class CourtDecisionDeleteView(LoginRequiredMixin, DeleteView):
    model = CourtDecision
    template_name = 'cases/courtdecision_confirm_delete.html'

    def get_success_url(self):
        return reverse('cases:hearing_detail', args=[self.object.hearing.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['hearing'] = self.object.hearing
        return context

class FeeCreateView(LoginRequiredMixin, CreateView):
    model = Fee
    fields = ['amount', 'fee_type', 'due_date', 'paid_date', 'is_paid', 'note', 'receipt']
    template_name = 'cases/fee_form.html'

    def dispatch(self, request, *args, **kwargs):
        self.case = Case.objects.get(pk=kwargs['case_pk'])
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        form.instance.case = self.case
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('cases:detail', args=[self.case.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['case'] = self.case
        return context

class FeeUpdateView(LoginRequiredMixin, UpdateView):
    model = Fee
    fields = ['amount', 'fee_type', 'due_date', 'paid_date', 'is_paid', 'note', 'receipt']
    template_name = 'cases/fee_form.html'

    def get_success_url(self):
        return reverse('cases:detail', args=[self.object.case.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['case'] = self.object.case
        context['is_update'] = True
        return context

class FeeDeleteView(LoginRequiredMixin, DeleteView):
    model = Fee
    template_name = 'cases/fee_confirm_delete.html'

    def get_success_url(self):
        return reverse('cases:detail', args=[self.object.case.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['case'] = self.object.case
        return context

class IsCaseLawyerOrAdmin(permissions.BasePermission):
    def has_object_permission(self, request, view, obj):
        # السماح للمشرفين دائماً
        if request.user.is_superuser or request.user.groups.filter(name='Admin').exists():
            return True
        # السماح فقط للمحامي المعيّن على القضية
        return obj.lawyer == request.user

class CaseViewSet(viewsets.ModelViewSet):
    queryset = Case.objects.all()
    serializer_class = CaseSerializer
    permission_classes = [permissions.IsAuthenticated, IsCaseLawyerOrAdmin]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['case_number', 'status', 'client', 'lawyer']
    search_fields = ['case_number', 'title', 'description']
    ordering_fields = ['case_number', 'title', 'status', 'start_date', 'end_date']

def case_accounts(request, case_id):
    case = get_object_or_404(Case, pk=case_id)
    fees = case.fees.all()
    payments = case.payments.all()
    fees_total = sum(f.amount for f in fees)
    paid = payments.aggregate(total=models.Sum('amount'))['total'] or 0
    unpaid = fees_total - paid
    return render(request, 'cases/case_accounts.html', {
        'case': case,
        'fees': fees,
        'payments': payments,
        'fees_total': fees_total,
        'paid': paid,
        'unpaid': unpaid,
    })

def add_case_payment(request, case_id):
    case = get_object_or_404(Case, pk=case_id)
    client = case.client
    if request.method == 'POST':
        form = PaymentForm(request.POST)
        if form.is_valid():
            payment = form.save(commit=False)
            payment.client = client
            payment.case = case
            payment.save()
            messages.success(request, 'تمت إضافة الدفعة بنجاح.')
            return redirect('cases:case_accounts', case_id=case.id)
    else:
        form = PaymentForm(initial={'client': client, 'case': case})
    return render(request, 'cases/add_case_payment.html', {'form': form, 'case': case, 'client': client})
