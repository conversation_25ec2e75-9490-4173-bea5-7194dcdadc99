# Generated by Django 5.0.14 on 2025-07-06 11:25

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0004_fee'),
        ('clients', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الدفعة')),
                ('notes', models.CharField(blank=True, max_length=255, verbose_name='ملاحظات')),
                ('case', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payments', to='cases.case', verbose_name='القضية')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='clients.client', verbose_name='العميل')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'الدفعات',
                'ordering': ['-date'],
            },
        ),
    ]
