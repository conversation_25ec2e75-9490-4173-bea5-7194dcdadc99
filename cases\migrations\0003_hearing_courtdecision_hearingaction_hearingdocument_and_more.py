# Generated by Django 5.0.14 on 2025-07-06 09:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Hearing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(verbose_name='تاريخ ووقت الجلسة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الجلسة')),
                ('location', models.CharField(blank=True, max_length=255, null=True, verbose_name='مكان الجلسة')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='hearings', to='cases.case', verbose_name='القضية')),
            ],
        ),
        migrations.CreateModel(
            name='CourtDecision',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField(verbose_name='نص القرار')),
                ('file', models.FileField(blank=True, null=True, upload_to='court_decisions/', verbose_name='ملف القرار')),
                ('date_issued', models.DateField(blank=True, null=True, verbose_name='تاريخ صدور القرار')),
                ('hearing', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='court_decision', to='cases.hearing', verbose_name='الجلسة')),
            ],
        ),
        migrations.CreateModel(
            name='HearingAction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(max_length=255, verbose_name='الإجراء')),
                ('is_completed', models.BooleanField(default=False, verbose_name='تم التنفيذ')),
                ('due_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')),
                ('hearing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='actions', to='cases.hearing', verbose_name='الجلسة')),
            ],
        ),
        migrations.CreateModel(
            name='HearingDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='hearing_documents/', verbose_name='ملف المستند')),
                ('description', models.CharField(blank=True, max_length=255, null=True, verbose_name='وصف المستند')),
                ('hearing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='cases.hearing', verbose_name='الجلسة')),
            ],
        ),
        migrations.CreateModel(
            name='HearingImportantDate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ المهم')),
                ('description', models.CharField(blank=True, max_length=255, null=True, verbose_name='وصف التاريخ')),
                ('hearing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='important_dates', to='cases.hearing', verbose_name='الجلسة')),
            ],
        ),
        migrations.CreateModel(
            name='HearingReminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reminder_date', models.DateTimeField(verbose_name='تاريخ ووقت التذكير')),
                ('note', models.CharField(blank=True, max_length=255, null=True, verbose_name='ملاحظة التذكير')),
                ('is_sent', models.BooleanField(default=False, verbose_name='تم إرسال التذكير')),
                ('hearing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reminders', to='cases.hearing', verbose_name='الجلسة')),
            ],
        ),
    ]
