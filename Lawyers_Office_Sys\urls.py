# urls.py
# law_firm_project/urls.py  
from django.contrib import admin
from django.urls import path, include # Make sure include is imported
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from rest_framework import permissions
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView
from django.http import JsonResponse
from appointments.models import Appointment
from cases.models import Hearing
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt

urlpatterns = [
    path('admin/', admin.site.urls),
    path('authentication/', include('authentication.urls')),
    path('', include('dashboard.urls')),
    path('clients/', include('clients.urls')),
    path('cases/', include('cases.urls')),
    path('appointments/', include('appointments.urls')),
    path('documents/', include('documents.urls')),
    path('reports/', include('reports.urls')),
    path('notifications/', include('notifications.urls', namespace='notifications')),
    path('settings/', include('settings_app.urls', namespace='settings_app')),
    path('search/', include('search.urls', namespace='search')),
    path('', include('core.urls')), # Include your app's URLs
    path('api/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('swagger/', get_schema_view(
        openapi.Info(
            title="Lawyers Office API",
            default_version='v1',
            description="توثيق API لنظام مكتب المحاماة",
        ),
        public=True,
        permission_classes=(permissions.AllowAny,),
    ).with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', get_schema_view(
        openapi.Info(
            title="Lawyers Office API",
            default_version='v1',
            description="توثيق API لنظام مكتب المحاماة",
        ),
        public=True,
        permission_classes=(permissions.AllowAny,),
    ).with_ui('redoc', cache_timeout=0), name='schema-redoc'),
]

@csrf_exempt
def calendar_events_api(request):
    events = []
    # المواعيد
    for appt in Appointment.objects.all():
        events.append({
            'title': f'موعد: {appt.title}',
            'start': appt.datetime.isoformat(),
            'end': appt.datetime.isoformat(),
            'url': f'/appointments/{appt.pk}/',
            'color': '#0d6efd',
        })
    # الجلسات
    for hearing in Hearing.objects.all():
        events.append({
            'title': f'جلسة: {hearing.case.case_number}',
            'start': hearing.date.isoformat(),
            'end': hearing.date.isoformat(),
            'url': f'/cases/hearing/{hearing.pk}/',
            'color': '#ffc107',
        })
    return JsonResponse(events, safe=False)

urlpatterns += [
    path('calendar/', TemplateView.as_view(template_name='calendar/calendar.html'), name='calendar'),
    path('calendar/events/', calendar_events_api, name='calendar_events_api'),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)