{% extends 'base.html' %}

{% block title %}خطأ في الخادم - 500{% endblock %}

{% block content %}
<div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
    <div class="text-center">
        <div class="error-code mb-4">
            <h1 class="display-1 fw-bold text-danger">500</h1>
        </div>
        
        <div class="error-message mb-4">
            <h2 class="h3 mb-3">خطأ في الخادم</h2>
            <p class="text-muted mb-4">
                عذراً، حدث خطأ غير متوقع في الخادم. نحن نعمل على حل هذه المشكلة.
            </p>
        </div>
        
        <div class="error-illustration mb-4">
            <i class="bi bi-exclamation-triangle display-1 text-danger opacity-50"></i>
        </div>
        
        <div class="error-actions">
            <a href="/" class="btn btn-primary me-2">
                <i class="bi bi-house me-2"></i>
                العودة للرئيسية
            </a>
            <button onclick="location.reload()" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-clockwise me-2"></i>
                إعادة المحاولة
            </button>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                إذا استمرت المشكلة، يرجى 
                <a href="/contact/" class="text-decoration-none">الاتصال بالدعم الفني</a>
            </small>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.error-code h1 {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.error-illustration {
    animation: shake 2s ease-in-out infinite;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
</style>
{% endblock %}
