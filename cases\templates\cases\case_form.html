{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-12 col-md-10 col-lg-8">
            <div class="card shadow rounded-4 border-0">
                <div class="card-header bg-primary text-white py-3 border-0 rounded-top-4">
                    <h3 class="mb-0 fw-bold">
                        <i class="bi bi-briefcase me-2"></i>
                        {% if object %}تعديل بيانات قضية{% else %}إضافة قضية جديدة{% endif %}
                    </h3>
                </div>
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- حقل نوع القضية -->
                        <div class="mb-3 animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                            <label class="form-label fw-bold">
                                نوع القضية <span class="text-danger">*</span>
                            </label>
                            <select class="form-select shadow-sm" name="case_type" required>
                                <option value="">-- اختر نوع القضية --</option>
                                <option value="جنح">جنح</option>
                                <option value="جنايات">جنايات</option>
                                <option value="أسرة">أسرة</option>
                                <option value="أحوال شخصية">أحوال شخصية</option>
                                <option value="تجارية">تجارية</option>
                                <option value="إدارية">إدارية</option>
                            </select>
                        </div>

                        <!-- حقل المحكمة -->
                        <div class="mb-3 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                            <label class="form-label fw-bold">
                                المحكمة <span class="text-danger">*</span>
                            </label>
                            <select class="form-select shadow-sm" name="court" required>
                                <option value="">-- اختر المحكمة --</option>
                                {% for court in courts %}
                                    <option value="{{ court.id }}">{{ court.name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- حقل صفة العميل -->
                        <div class="mb-3 animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                            <label class="form-label fw-bold">
                                صفة العميل <span class="text-danger">*</span>
                            </label>
                            <select class="form-select shadow-sm" name="client_role" required>
                                <option value="">-- اختر الصفة --</option>
                                <option value="مدعي">مدعي</option>
                                <option value="مدعى عليه"> مدعى عليه</option>
                                <option value="متهم">متهم</option>
                                <option value="مدع">مدع</option>
                                <option value="مشتكى عليه">مشتكى عليه</option>
                            </select>
                        </div>

                        {% for field in form %}
                            <div class="mb-4 animate__animated animate__fadeInUp" style="animation-delay: {{ forloop.counter|add:'0.4' }}s">
                                <label for="{{ field.id_for_label }}" class="form-label fw-bold mb-2">
                                    {{ field.label }}
                                    {% if field.field.required %}<span class="text-danger">*</span>{% endif %}
                                </label>
                                {% if field.field.widget.input_type == 'text' and 'date' in field.name %}
                                    <input type="date" 
                                           class="form-control shadow-sm" 
                                           name="{{ field.name }}" 
                                           id="{{ field.id_for_label }}" 
                                           {% if field.field.required %}required{% endif %}
                                           value="{{ field.value|default_if_none:'' }}">
                                {% elif field.name in 'case_number title brief_description client assigned_lawyer' %}
                                    {% if field.field.widget.input_type == 'select' %}
                                        <select class="form-select shadow-sm" 
                                               name="{{ field.name }}" 
                                               id="{{ field.id_for_label }}"
                                               {% if field.field.required %}required{% endif %}>
                                            {{ field.field.widget.choices }}
                                        </select>
                                    {% else %}
                                        <input type="{{ field.field.widget.input_type|default:'text' }}" 
                                              class="form-control shadow-sm" 
                                              name="{{ field.name }}" 
                                              id="{{ field.id_for_label }}" 
                                              value="{{ field.value|default_if_none:'' }}"
                                              {% if field.field.required %}required{% endif %}
                                              {% if field.field.max_length %}maxlength="{{ field.field.max_length }}"{% endif %}>
                                    {% endif %}
                                {% else %}
                                    {{ field }}
                                {% endif %}
                                {% if field.help_text %}
                                    <small class="text-muted mt-1 d-block">{{ field.help_text }}</small>
                                {% endif %}
                                {% if field.errors %}
                                    <div class="invalid-feedback d-block mt-1">{{ field.errors|striptags }}</div>
                                {% endif %}
                            </div>
                        {% endfor %}

                        <div class="d-flex justify-content-between mt-4">
                            <button type="submit" class="btn btn-primary px-6 py-3 rounded-pill shadow animate__animated animate__fadeIn" style="--animate-duration: 0.5s">
                                <i class="bi bi-check-circle me-2"></i>
                                {% if object %}تحديث{% else %}إضافة{% endif %}
                            </button>
                            <a href="{% url 'cases:list' %}" class="btn btn-outline-secondary px-6 py-3 rounded-pill animate__animated animate__fadeIn" style="--animate-duration: 0.6s">
                                <i class="bi bi-arrow-left-circle me-2"></i> العودة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
<style>
.card {
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.form-control, .form-select {
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    border: 1px solid #dee2e6;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, 
.form-select:focus,
input[type="date"]:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.invalid-feedback {
    animation: fadeIn 0.3s;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
{% endblock %}
