from django.urls import path
from . import views

app_name = 'users'

urlpatterns = [
    # User management (admin/staff only)
    path('', views.user_list, name='user_list'),
    path('create/', views.user_create, name='user_create'),
    path('<int:user_id>/', views.user_detail, name='user_detail'),
    path('<int:user_id>/edit/', views.user_edit, name='user_edit'),
    path('<int:user_id>/delete/', views.user_delete, name='user_delete'),
    
    # User profile (self-service)
    path('profile/', views.user_profile, name='profile'),
    path('profile/change-password/', views.change_password, name='change_password'),
    
    # Role management
    path('roles/', views.role_list, name='role_list'),
    path('roles/create/', views.role_create, name='role_create'),
    path('roles/<int:role_id>/', views.role_detail, name='role_detail'),
    path('roles/<int:role_id>/edit/', views.role_edit, name='role_edit'),
    path('roles/<int:role_id>/delete/', views.role_delete, name='role_delete'),
    
    # Bulk actions
    path('bulk-action/', views.bulk_user_action, name='bulk_action'),
    
    # API endpoints
    path('api/search/', views.user_search_api, name='user_search_api'),
    path('api/stats/', views.user_stats_api, name='user_stats_api'),
    path('api/<int:user_id>/toggle-active/', views.toggle_user_active, name='toggle_active'),
    path('api/<int:user_id>/reset-password/', views.reset_user_password, name='reset_password'),
]
