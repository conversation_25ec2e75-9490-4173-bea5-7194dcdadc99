# core/views.py
from django.shortcuts import render
from django.views.generic import ListView
from .models import Case

class CaseListView(ListView):
    model = Case  # Specify the model to use
    template_name = 'core/case_list.html'  # The template to render
    context_object_name = 'cases'  # The name of the list in the template


# core/views.py
from django.contrib.auth.mixins import LoginRequiredMixin

class CaseListView(LoginRequiredMixin, ListView): # Add LoginRequiredMixin
    model = Case
    template_name = 'core/case_list.html'
    context_object_name = 'cases'

    def get_queryset(self):
        # Override the default queryset to filter cases
        # for the currently logged-in lawyer.
        return Case.objects.filter(lawyers__user=self.request.user)