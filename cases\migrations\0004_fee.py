# Generated by Django 5.0.14 on 2025-07-06 09:43

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0003_hearing_courtdecision_hearingaction_hearingdocument_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Fee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ')),
                ('fee_type', models.CharField(choices=[('advance', 'دفعة مقدمة'), ('final', 'دفعة نهائية'), ('session', 'أتعاب جلسة'), ('expense', 'مصاريف'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الأتعاب')),
                ('due_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')),
                ('paid_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الدفع')),
                ('is_paid', models.BooleanField(default=False, verbose_name='تم الدفع')),
                ('note', models.CharField(blank=True, max_length=255, null=True, verbose_name='ملاحظات')),
                ('receipt', models.FileField(blank=True, null=True, upload_to='fee_receipts/', verbose_name='إيصال الدفع')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fees', to='cases.case', verbose_name='القضية')),
            ],
        ),
    ]
