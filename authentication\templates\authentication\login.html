{% extends 'base.html' %}
{% load static %}
{% block content %}
<div class="container d-flex align-items-center justify-content-center min-vh-100">
    <div class="card border-0 p-4 shadow-lg animate__animated animate__fadeIn" style="min-width:350px; max-width:400px; width:100%;">
        <div class="text-center mb-4">
            <img src="{% static 'logo.png' %}" alt="شعار مكتب المحاماة" style="height:70px;" class="mb-3">
            <h4 class="fw-bold text-primary">تسجيل الدخول للنظام</h4>
            <small class="text-muted">أدخل بيانات الدخول للوصول إلى لوحة التحكم</small>
        </div>
        <form method="post" novalidate>
            {% csrf_token %}
            {% if form.errors %}
                <div class="alert alert-danger animate__animated animate__headShake">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    اسم المستخدم أو كلمة المرور غير صحيحة
                </div>
            {% endif %}
            <div class="mb-3">
                <label for="id_username" class="form-label fw-bold">اسم المستخدم</label>
                <input type="text" name="username" autofocus required 
                       class="form-control shadow-sm rounded-pill"
                       id="id_username" placeholder="أدخل اسم المستخدم">
            </div>
            <div class="mb-3">
                <label for="id_password" class="form-label fw-bold">كلمة المرور</label>
                <input type="password" name="password" required 
                       class="form-control shadow-sm rounded-pill"
                       id="id_password" placeholder="••••••••">
            </div>
            <button type="submit" class="btn btn-primary w-100 rounded-pill py-2 mt-3 shadow-sm" id="loginBtn">
                <i class="bi bi-box-arrow-in-right me-2"></i> دخول إلى النظام
            </button>
        </form>
        <div class="d-flex justify-content-between mt-4">
            <span class="theme-toggle cursor-pointer" onclick="toggleTheme()" title="تبديل الوضع الليلي/النهاري">
                <i id="themeIcon" class="bi bi-moon-fill"></i>
            </span>
            <small class="text-muted">v1.0.0</small>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
<style>
    body[data-theme='dark'] .card {
        background: linear-gradient(135deg, #2b2d42 0%, #1a1a2e 100%);
        color: #ffffff;
    }
    
    body[data-theme='light'] .card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    }
    
    #loginBtn {
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    #loginBtn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3) !important;
    }
    
    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
    
    .theme-toggle {
        transition: color 0.2s;
    }
    
    .theme-toggle:hover {
        color: #0d6efd;
    }
</style>
{% endblock %}
