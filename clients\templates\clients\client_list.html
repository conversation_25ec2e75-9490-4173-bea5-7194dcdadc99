{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="fw-bold"><i class="bi bi-people text-primary me-2"></i>العملاء</h3>
        <a href="{% url 'clients:add' %}" class="btn btn-gradient-gold">
            <i class="bi bi-person-plus"></i> إضافة عميل جديد
        </a>
    </div>
    <div class="card shadow rounded-4 border-0 p-3">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>الاسم الكامل</th>
                        <th>رقم الهوية</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for client in clients %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td><a href="{% url 'clients:detail' client.pk %}" class="fw-bold text-decoration-none">{{ client.full_name }}</a></td>
                        <td>{{ client.national_id }}</td>
                        <td>{{ client.phone }}</td>
                        <td>{{ client.email }}</td>
                        <td>
                            <a href="{% url 'clients:edit' client.pk %}" class="btn btn-sm btn-outline-primary"><i class="bi bi-pencil-square"></i></a>
                            <a href="{% url 'clients:delete' client.pk %}" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr><td colspan="6" class="text-center">لا يوجد عملاء بعد.</td></tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
<style>
.btn-gradient-gold {
    background: linear-gradient(90deg, #ffd600 0%, #ffb300 100%);
    color: #222b45;
    border: none;
    font-weight: bold;
    transition: box-shadow 0.2s;
}
.btn-gradient-gold:hover {
    box-shadow: 0 2px 12px #ffd60055;
    color: #151a30;
}
</style>
{% endblock %} 