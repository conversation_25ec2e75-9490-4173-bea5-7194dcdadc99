{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="fw-bold"><i class="bi bi-file-earmark-text text-primary me-2"></i>المستندات</h3>
        <a href="{% url 'documents:add' %}" class="btn btn-gradient-gold">
            <i class="bi bi-file-earmark-plus"></i> إضافة مستند جديد
        </a>
    </div>
    <div class="card shadow rounded-4 border-0 p-3">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>اسم المستند</th>
                        <th>القضية</th>
                        <th>العميل</th>
                        <th>تاريخ الرفع</th>
                        <th>تحميل</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for document in documents %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td><a href="{% url 'documents:detail' document.pk %}" class="fw-bold text-decoration-none">{{ document.name }}</a></td>
                        <td>{{ document.case }}</td>
                        <td>{{ document.client }}</td>
                        <td>{{ document.uploaded_at|date:'Y-m-d H:i' }}</td>
                        <td>
                            {% if document.file %}
                            <a href="{{ document.file.url }}" class="btn btn-sm btn-secondary" download>تحميل</a>
                            {% else %}-{% endif %}
                        </td>
                        <td>
                            <a href="{% url 'documents:edit' document.pk %}" class="btn btn-sm btn-outline-primary"><i class="bi bi-pencil-square"></i></a>
                            <a href="{% url 'documents:delete' document.pk %}" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr><td colspan="7" class="text-center">لا يوجد مستندات بعد.</td></tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
<style>
.btn-gradient-gold {
    background: linear-gradient(90deg, #ffd600 0%, #ffb300 100%);
    color: #222b45;
    border: none;
    font-weight: bold;
    transition: box-shadow 0.2s;
}
.btn-gradient-gold:hover {
    box-shadow: 0 2px 12px #ffd60055;
    color: #151a30;
}
</style>
{% endblock %} 