# Environment Configuration for Lawyers Office System
# Copy this file to .env and update the values according to your environment

# Django Settings
SECRET_KEY=your-secret-key-here-change-in-production
DEBUG=False
DJANGO_SETTINGS_MODULE=Lawyers_Office_Sys.settings.production

# Allowed Hosts (comma-separated)
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com,www.your-domain.com

# Database Configuration
# For SQL Server
DATABASE_URL=mssql://username:password@server:port/database?driver=ODBC+Driver+18+for+SQL+Server&TrustServerCertificate=yes

# For PostgreSQL (alternative)
# DATABASE_URL=postgresql://username:password@server:port/database

# For SQLite (development only)
# DATABASE_URL=sqlite:///db.sqlite3

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Site Configuration
SITE_URL=https://your-domain.com
SITE_NAME=نظام مكتب المحاماة

# Security Settings
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True

# File Upload Settings
MAX_UPLOAD_SIZE=104857600  # 100MB in bytes
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif

# Backup Settings
BACKUP_ENABLED=True
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Monitoring and Logging
SENTRY_DSN=your-sentry-dsn-here
LOG_LEVEL=INFO

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_NAME=System Administrator

# API Configuration
API_RATE_LIMIT=1000/hour
API_THROTTLE_ANON=100/hour
API_THROTTLE_USER=1000/hour

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Cache Configuration
CACHE_BACKEND=redis
CACHE_LOCATION=redis://localhost:6379/1

# Social Authentication (if needed)
# GOOGLE_OAUTH2_KEY=your-google-oauth2-key
# GOOGLE_OAUTH2_SECRET=your-google-oauth2-secret

# SMS Configuration (if needed)
# SMS_PROVIDER=twilio
# TWILIO_ACCOUNT_SID=your-twilio-account-sid
# TWILIO_AUTH_TOKEN=your-twilio-auth-token
# TWILIO_PHONE_NUMBER=your-twilio-phone-number

# Cloud Storage (if using AWS S3 or similar)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_STORAGE_BUCKET_NAME=your-bucket-name
# AWS_S3_REGION_NAME=your-region

# Development Settings (only for development environment)
# DEBUG_TOOLBAR=True
# SHELL_PLUS_PRINT_SQL=True

# Docker-specific settings
POSTGRES_DB=lawyers_office_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-postgres-password
POSTGRES_HOST=db
POSTGRES_PORT=5432

# SQL Server Docker settings
MSSQL_SA_PASSWORD=YourPassword123
MSSQL_PID=Express

# Nginx settings
NGINX_HOST=your-domain.com
NGINX_PORT=80
NGINX_SSL_PORT=443

# SSL Certificate paths (for production)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Timezone
TIME_ZONE=Asia/Riyadh

# Language and Localization
LANGUAGE_CODE=ar
USE_I18N=True
USE_L10N=True
USE_TZ=True

# PWA Settings
PWA_APP_NAME=نظام مكتب المحاماة
PWA_APP_DESCRIPTION=نظام إدارة شامل لمكاتب المحاماة
PWA_APP_THEME_COLOR=#222b45
PWA_APP_BACKGROUND_COLOR=#ffffff

# Performance Settings
CONN_MAX_AGE=60
DATABASE_CONN_MAX_AGE=60

# Feature Flags
ENABLE_API=True
ENABLE_NOTIFICATIONS=True
ENABLE_REPORTS=True
ENABLE_SEARCH=True
ENABLE_CALENDAR=True

# Third-party Integrations
# GOOGLE_MAPS_API_KEY=your-google-maps-api-key
# PAYMENT_GATEWAY_KEY=your-payment-gateway-key
# DOCUMENT_SIGNING_API_KEY=your-document-signing-api-key
