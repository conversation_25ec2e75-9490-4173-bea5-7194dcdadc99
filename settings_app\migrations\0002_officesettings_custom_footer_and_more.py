# Generated by Django 5.0.14 on 2025-07-01 11:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings_app', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='officesettings',
            name='custom_footer',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='نص تذييل مخصص'),
        ),
        migrations.AddField(
            model_name='officesettings',
            name='default_from_email',
            field=models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الافتراضي للإرسال'),
        ),
        migrations.AddField(
            model_name='officesettings',
            name='enable_email_notifications',
            field=models.BooleanField(default=True, verbose_name='تفعيل إشعارات البريد الإلكتروني'),
        ),
        migrations.AddField(
            model_name='officesettings',
            name='enable_system_notifications',
            field=models.BooleanField(default=True, verbose_name='تفعيل إشعارات النظام'),
        ),
        migrations.AddField(
            model_name='officesettings',
            name='reports_page_size',
            field=models.PositiveIntegerField(default=20, verbose_name='عدد العناصر في صفحة التقارير'),
        ),
        migrations.AddField(
            model_name='officesettings',
            name='secondary_logo',
            field=models.ImageField(blank=True, null=True, upload_to='settings/', verbose_name='شعار ثانوي'),
        ),
    ]
