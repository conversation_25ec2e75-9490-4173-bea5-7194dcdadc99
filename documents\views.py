from django.shortcuts import render
from django.urls import reverse_lazy, reverse
from django.views.generic import ListView, CreateView, UpdateView, DetailView, DeleteView
from .models import Document
from notifications.utils import create_notification
from django.contrib.auth.mixins import PermissionRequiredMixin
from activitylog.utils import log_activity
from rest_framework import viewsets, permissions
from .serializers import DocumentSerializer
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from django.core.mail import send_mail

# Create your views here.

class DocumentListView(ListView):
    model = Document
    template_name = 'documents/document_list.html'
    context_object_name = 'documents'

class DocumentCreateView(PermissionRequiredMixin, CreateView):
    permission_required = 'documents.add_document'
    model = Document
    fields = ['name', 'file', 'case', 'client', 'notes']
    template_name = 'documents/document_form.html'
    success_url = reverse_lazy('documents:list')
    def get_form(self, *args, **kwargs):
        form = super().get_form(*args, **kwargs)
        form.enctype = 'multipart/form-data'
        return form
    def form_valid(self, form):
        response = super().form_valid(form)
        doc = self.object
        # إشعار للعميل عبر البريد الإلكتروني فقط
        if doc.client and doc.client.email:
            send_mail(
                subject='تم إضافة مستند جديد',
                message=f'تم إضافة المستند "{doc.name}".',
                from_email=None,  # سيستخدم DEFAULT_FROM_EMAIL من الإعدادات
                recipient_list=[doc.client.email],
                fail_silently=True,
            )
        # إشعار للمحامي
        if doc.case and doc.case.lawyer:
            create_notification(
                user=doc.case.lawyer,
                title='تم إضافة مستند جديد لقضية تخصك',
                message=f'تم إضافة المستند "{doc.name}" للقضية "{doc.case.title}".',
                url=reverse('documents:detail', args=[doc.pk]),
                notification_type='document',
                send_email=True
            )
        log_activity(self.request.user, 'document', doc.pk, 'create', f'إضافة مستند: {doc.name}')
        return response

class DocumentUpdateView(PermissionRequiredMixin, UpdateView):
    permission_required = 'documents.change_document'
    model = Document
    fields = ['name', 'file', 'case', 'client', 'notes']
    template_name = 'documents/document_form.html'
    success_url = reverse_lazy('documents:list')
    def get_form(self, *args, **kwargs):
        form = super().get_form(*args, **kwargs)
        form.enctype = 'multipart/form-data'
        return form
    def form_valid(self, form):
        response = super().form_valid(form)
        doc = self.object
        # إشعار للعميل عبر البريد الإلكتروني فقط
        if doc.client and doc.client.email:
            send_mail(
                subject='تم تعديل مستند',
                message=f'تم تعديل المستند "{doc.name}".',
                from_email=None,  # سيستخدم DEFAULT_FROM_EMAIL من الإعدادات
                recipient_list=[doc.client.email],
                fail_silently=True,
            )
        # إشعار للمحامي
        if doc.case and doc.case.lawyer:
            create_notification(
                user=doc.case.lawyer,
                title='تم تعديل مستند في قضية تخصك',
                message=f'تم تعديل المستند "{doc.name}" للقضية "{doc.case.title}".',
                url=reverse('documents:detail', args=[doc.pk]),
                notification_type='document',
                send_email=True
            )
        log_activity(self.request.user, 'document', doc.pk, 'update', f'تعديل مستند: {doc.name}')
        return response

class DocumentDetailView(DetailView):
    model = Document
    template_name = 'documents/document_detail.html'
    context_object_name = 'document'

class DocumentDeleteView(PermissionRequiredMixin, DeleteView):
    permission_required = 'documents.delete_document'
    model = Document
    template_name = 'documents/document_confirm_delete.html'
    success_url = reverse_lazy('documents:list')

    def delete(self, request, *args, **kwargs):
        doc = self.get_object()
        log_activity(request.user, 'document', doc.pk, 'delete', f'حذف مستند: {doc.name}')
        return super().delete(request, *args, **kwargs)

class IsDocumentOwnerOrAdmin(permissions.BasePermission):
    def has_object_permission(self, request, view, obj):
        if request.user.is_superuser or request.user.groups.filter(name='Admin').exists():
            return True
        # السماح للمحامي المرتبط بالقضية
        if obj.case and obj.case.lawyer == request.user:
            return True
        # لا تتحقق من obj.client.user
        return False

class DocumentViewSet(viewsets.ModelViewSet):
    queryset = Document.objects.all()
    serializer_class = DocumentSerializer
    permission_classes = [permissions.IsAuthenticated, IsDocumentOwnerOrAdmin]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['case', 'client', 'uploaded_at']
    search_fields = ['name', 'notes']
    ordering_fields = ['name', 'uploaded_at']
