from django.db import models
from django.conf import settings

class ActivityLog(models.Model):
    ENTITY_TYPES = [
        ('case', 'قضية'),
        ('document', 'مستند'),
        ('appointment', 'موعد'),
        ('client', 'عميل'),
        ('user', 'مستخدم'),
        ('other', 'أخرى'),
    ]
    ACTION_TYPES = [
        ('create', 'إضافة'),
        ('update', 'تعديل'),
        ('delete', 'حذف'),
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
        ('other', 'أخرى'),
    ]
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    entity_type = models.CharField(max_length=20, choices=ENTITY_TYPES)
    entity_id = models.CharField(max_length=50)
    action = models.CharField(max_length=20, choices=ACTION_TYPES)
    description = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.get_entity_type_display()} - {self.get_action_display()} - {self.timestamp}"
