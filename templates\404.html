{% extends 'base.html' %}

{% block title %}الصفحة غير موجودة - 404{% endblock %}

{% block content %}
<div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
    <div class="text-center">
        <div class="error-code mb-4">
            <h1 class="display-1 fw-bold text-primary">404</h1>
        </div>
        
        <div class="error-message mb-4">
            <h2 class="h3 mb-3">الصفحة غير موجودة</h2>
            <p class="text-muted mb-4">
                عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
            </p>
        </div>
        
        <div class="error-illustration mb-4">
            <i class="bi bi-file-earmark-x display-1 text-muted opacity-50"></i>
        </div>
        
        <div class="error-actions">
            <a href="/" class="btn btn-primary me-2">
                <i class="bi bi-house me-2"></i>
                العودة للرئيسية
            </a>
            <button onclick="history.back()" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i>
                العودة للخلف
            </button>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                إذا كنت تعتقد أن هذا خطأ، يرجى 
                <a href="/contact/" class="text-decoration-none">الاتصال بالدعم الفني</a>
            </small>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.error-code h1 {
    background: linear-gradient(135deg, #0d6efd, #6610f2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);
}

.error-illustration {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
</style>
{% endblock %}
