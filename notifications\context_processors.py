from .models import Notification

def notifications_context(request):
    if request.user.is_authenticated:
        notifications = Notification.objects.filter(user=request.user).order_by('-created_at')[:5]
        notifications_unread_count = Notification.objects.filter(user=request.user, is_read=False).count()
    else:
        notifications = []
        notifications_unread_count = 0
    return {
        'notifications': notifications,
        'notifications_unread_count': notifications_unread_count,
    } 