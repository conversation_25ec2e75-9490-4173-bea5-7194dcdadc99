# Lawyers Office System Information

## Summary
A comprehensive law office management system built with Django. The system provides functionality for managing clients, cases, appointments, documents, and reports for a law firm. It includes user authentication, dashboard analytics, notification system, and activity logging.

## Structure
- **Lawyers_Office_Sys/**: Core Django project configuration
- **users/**: User authentication and management
- **clients/**: Client information management
- **cases/**: Legal case management
- **appointments/**: Scheduling and appointment management
- **documents/**: Document storage and management
- **dashboard/**: Main dashboard and analytics
- **reports/**: Reporting functionality
- **notifications/**: User notification system
- **activitylog/**: Activity tracking and logging
- **search/**: Search functionality across the system
- **settings_app/**: System configuration and settings
- **authentication/**: Authentication views and forms
- **templates/**: Shared templates for the application

## Language & Runtime
**Language**: Python
**Framework**: Django (likely 5.0 based on settings comments)
**Database**: Microsoft SQL Server
**API**: REST API with Django REST Framework
**Documentation**: Swagger/OpenAPI (drf-yasg)

## Dependencies
**Main Dependencies**:
- Django
- Django REST Framework
- django-cors-headers
- djangorestframework-simplejwt
- drf-yasg (API documentation)
- MSSQL database driver

## Build & Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Run development server
python manage.py runserver
```

## API & Integration
**REST API**: Full REST API with JWT authentication
**Endpoints**:
- `/api/token/`: JWT token generation
- `/api/token/refresh/`: JWT token refresh
- `/swagger/`: API documentation with Swagger UI
- `/redoc/`: API documentation with ReDoc

## Application Components

### Authentication System
- Custom user model extending Django's AbstractUser
- JWT-based authentication for API access
- Traditional session-based authentication for web interface

### Client Management
- Client information storage and retrieval
- Client relationship tracking
- REST API endpoints for client data

### Case Management
- Case tracking with status (open, closed, pending)
- Case assignment to lawyers
- Case-client relationship management
- Timeline and history tracking

### Document Management
- Document storage and organization
- Document categorization and tagging
- Document versioning

### Appointment System
- Scheduling appointments with clients
- Calendar integration
- Reminder functionality

### Reporting System
- Generate reports on cases, clients, and activities
- Export functionality
- Data visualization

### Notification System
- User notifications for important events
- Custom context processor for template integration

### Activity Logging
- Track user activities within the system
- Audit trail for compliance purposes

### Internationalization
- Arabic language support (default language)
- Multilingual capability through Django's i18n