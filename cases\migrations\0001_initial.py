# Generated by Django 5.0.14 on 2025-07-01 10:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('clients', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Case',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('case_number', models.CharField(max_length=100, unique=True, verbose_name='رقم القضية')),
                ('title', models.CharField(max_length=255, verbose_name='عنوان القضية')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف مختصر')),
                ('start_date', models.DateField(verbose_name='تاريخ البدء')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإغلاق')),
                ('status', models.CharField(choices=[('open', 'مفتوحة'), ('closed', 'مغلقة'), ('pending', 'معلقة')], default='open', max_length=10, verbose_name='الحالة')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cases', to='clients.client', verbose_name='العميل')),
            ],
        ),
    ]
