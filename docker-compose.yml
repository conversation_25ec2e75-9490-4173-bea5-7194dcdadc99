version: '3.8'

services:
  # Main Django Application
  web:
    build: .
    container_name: lawyers_office_web
    ports:
      - "8000:8000"
    volumes:
      - ./media:/app/media
      - ./logs:/app/logs
    environment:
      - DJANGO_SETTINGS_MODULE=Lawyers_Office_Sys.settings.production
      - DATABASE_URL=mssql://sa:YourPassword123@db:1433/LawyersOfficeDB?driver=ODBC+Driver+18+for+SQL+Server&TrustServerCertificate=yes
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
      - DEBUG=False
      - ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - lawyers_office_network

  # SQL Server Database
  db:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: lawyers_office_db
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourPassword123
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - mssql_data:/var/opt/mssql
    restart: unless-stopped
    networks:
      - lawyers_office_network

  # Redis for Caching and Sessions
  redis:
    image: redis:7-alpine
    container_name: lawyers_office_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - lawyers_office_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: lawyers_office_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./media:/app/media:ro
      - ./static:/app/static:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - web
    restart: unless-stopped
    networks:
      - lawyers_office_network

  # Celery Worker for Background Tasks
  celery:
    build: .
    container_name: lawyers_office_celery
    command: celery -A Lawyers_Office_Sys worker -l info
    volumes:
      - ./media:/app/media
      - ./logs:/app/logs
    environment:
      - DJANGO_SETTINGS_MODULE=Lawyers_Office_Sys.settings.production
      - DATABASE_URL=mssql://sa:YourPassword123@db:1433/LawyersOfficeDB?driver=ODBC+Driver+18+for+SQL+Server&TrustServerCertificate=yes
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - lawyers_office_network

  # Celery Beat for Scheduled Tasks
  celery-beat:
    build: .
    container_name: lawyers_office_celery_beat
    command: celery -A Lawyers_Office_Sys beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    volumes:
      - ./media:/app/media
      - ./logs:/app/logs
    environment:
      - DJANGO_SETTINGS_MODULE=Lawyers_Office_Sys.settings.production
      - DATABASE_URL=mssql://sa:YourPassword123@db:1433/LawyersOfficeDB?driver=ODBC+Driver+18+for+SQL+Server&TrustServerCertificate=yes
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - lawyers_office_network

  # Flower for Celery Monitoring
  flower:
    build: .
    container_name: lawyers_office_flower
    command: celery -A Lawyers_Office_Sys flower
    ports:
      - "5555:5555"
    environment:
      - DJANGO_SETTINGS_MODULE=Lawyers_Office_Sys.settings.production
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - lawyers_office_network

volumes:
  mssql_data:
    driver: local
  redis_data:
    driver: local

networks:
  lawyers_office_network:
    driver: bridge
