from django.db import models
from clients.models import Client
from users.models import User

class Case(models.Model):
    STATUS_CHOICES = [
        ('open', 'مفتوحة'),
        ('closed', 'مغلقة'),
        ('pending', 'معلقة'),
    ]
    CASE_TYPE_CHOICES = [
        ('plaintiff', 'مدعي'),
        ('defendant', 'مدعي عليه'),
    ]
    case_number = models.CharField(max_length=100, unique=True, verbose_name='رقم القضية')
    title = models.CharField(max_length=255, verbose_name='عنوان القضية')
    description = models.TextField(verbose_name='وصف مختصر', blank=True, null=True)
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='cases', verbose_name='العميل')
    lawyer = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='cases', verbose_name='المحامي المسؤول')
    start_date = models.DateField(verbose_name='تاريخ البدء')
    end_date = models.DateField(verbose_name='تاريخ الإغلاق', blank=True, null=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='open', verbose_name='الحالة')
    case_type = models.CharField(max_length=20, choices=CASE_TYPE_CHOICES, default='plaintiff', verbose_name='نوع القضية')

    def __str__(self):
        return f"{self.case_number} - {self.title}"

class Hearing(models.Model):
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='hearings', verbose_name='القضية')
    date = models.DateTimeField(verbose_name='تاريخ ووقت الجلسة')
    description = models.TextField(verbose_name='وصف الجلسة', blank=True, null=True)
    location = models.CharField(max_length=255, verbose_name='مكان الجلسة', blank=True, null=True)

    def __str__(self):
        return f"جلسة {self.case.case_number} بتاريخ {self.date.strftime('%Y-%m-%d %H:%M')}"

class HearingDocument(models.Model):
    hearing = models.ForeignKey(Hearing, on_delete=models.CASCADE, related_name='documents', verbose_name='الجلسة')
    file = models.FileField(upload_to='hearing_documents/', verbose_name='ملف المستند')
    description = models.CharField(max_length=255, verbose_name='وصف المستند', blank=True, null=True)

    def __str__(self):
        return f"مستند لجلسة {self.hearing}"

class CourtDecision(models.Model):
    hearing = models.OneToOneField(Hearing, on_delete=models.CASCADE, related_name='court_decision', verbose_name='الجلسة')
    text = models.TextField(verbose_name='نص القرار')
    file = models.FileField(upload_to='court_decisions/', verbose_name='ملف القرار', blank=True, null=True)
    date_issued = models.DateField(verbose_name='تاريخ صدور القرار', blank=True, null=True)

    def __str__(self):
        return f"قرار محكمة لجلسة {self.hearing}"

class HearingAction(models.Model):
    hearing = models.ForeignKey(Hearing, on_delete=models.CASCADE, related_name='actions', verbose_name='الجلسة')
    action = models.CharField(max_length=255, verbose_name='الإجراء')
    is_completed = models.BooleanField(default=False, verbose_name='تم التنفيذ')
    due_date = models.DateField(verbose_name='تاريخ الاستحقاق', blank=True, null=True)

    def __str__(self):
        return f"إجراء: {self.action} (جلسة {self.hearing})"

class HearingImportantDate(models.Model):
    hearing = models.ForeignKey(Hearing, on_delete=models.CASCADE, related_name='important_dates', verbose_name='الجلسة')
    date = models.DateField(verbose_name='التاريخ المهم')
    description = models.CharField(max_length=255, verbose_name='وصف التاريخ', blank=True, null=True)

    def __str__(self):
        return f"تاريخ مهم: {self.date} (جلسة {self.hearing})"

class HearingReminder(models.Model):
    hearing = models.ForeignKey(Hearing, on_delete=models.CASCADE, related_name='reminders', verbose_name='الجلسة')
    reminder_date = models.DateTimeField(verbose_name='تاريخ ووقت التذكير')
    note = models.CharField(max_length=255, verbose_name='ملاحظة التذكير', blank=True, null=True)
    is_sent = models.BooleanField(default=False, verbose_name='تم إرسال التذكير')

    def __str__(self):
        return f"تذكير لجلسة {self.hearing} في {self.reminder_date}"

class Fee(models.Model):
    FEE_TYPE_CHOICES = [
        ('advance', 'دفعة مقدمة'),
        ('final', 'دفعة نهائية'),
        ('session', 'أتعاب جلسة'),
        ('expense', 'مصاريف'),
        ('other', 'أخرى'),
    ]
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='fees', verbose_name='القضية')
    amount = models.DecimalField(max_digits=12, decimal_places=2, verbose_name='المبلغ')
    fee_type = models.CharField(max_length=20, choices=FEE_TYPE_CHOICES, verbose_name='نوع الأتعاب')
    due_date = models.DateField(verbose_name='تاريخ الاستحقاق', blank=True, null=True)
    paid_date = models.DateField(verbose_name='تاريخ الدفع', blank=True, null=True)
    is_paid = models.BooleanField(default=False, verbose_name='تم الدفع')
    note = models.CharField(max_length=255, verbose_name='ملاحظات', blank=True, null=True)
    receipt = models.FileField(upload_to='fee_receipts/', verbose_name='إيصال الدفع', blank=True, null=True)

    def __str__(self):
        return f"{self.case} - {self.amount} ({self.get_fee_type_display()})"
