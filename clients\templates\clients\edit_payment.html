{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-md-7 col-lg-6">
            <div class="card shadow rounded-4 border-0 p-4">
                <h4 class="mb-3 fw-bold"><i class="bi bi-pencil text-primary me-2"></i>تعديل الدفعة للعميل: {{ client.full_name }}</h4>
                <form method="post" novalidate>
                    {% csrf_token %}
                    {{ form.as_p }}
                    <div class="d-flex justify-content-between mt-3">
                        <button type="submit" class="btn btn-primary px-4"><i class="bi bi-check-circle"></i> حفظ التعديلات</button>
                        <a href="{% url 'clients:client_payments' client.id %}" class="btn btn-link"><i class="bi bi-arrow-right-circle"></i> العودة للدفعات</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %} 