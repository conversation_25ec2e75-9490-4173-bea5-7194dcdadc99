# نظام مكتب المحاماة - Lawyers Office System

نظام إدارة شامل ومتطور لمكاتب المحاماة مبني بتقنية Django مع دعم كامل للغة العربية وتصميم متجاوب.

## المميزات الرئيسية

### 🏢 إدارة شاملة
- **إدارة القضايا**: تتبع شامل للقضايا مع الجلسات والمرافعات
- **إدارة العملاء**: ملفات شخصية مفصلة للعملاء مع تاريخ القضايا
- **إدارة المواعيد**: نظام مواعيد متقدم مع التذكيرات التلقائية
- **إدارة المستندات**: رفع وتنظيم المستندات مع نظام فئات متقدم

### 📊 التقارير والإحصائيات
- لوحة تحكم تفاعلية مع إحصائيات مباشرة
- تقارير مفصلة للقضايا والعملاء والمالية
- رسوم بيانية تفاعلية لتحليل الأداء
- تصدير التقارير بصيغ مختلفة (PDF, Excel)

### 🔍 البحث المتقدم
- بحث شامل عبر جميع البيانات
- فلاتر متقدمة حسب التاريخ والنوع والحالة
- اقتراحات البحث الذكية
- واجهة برمجة تطبيقات للبحث

### 👥 إدارة المستخدمين
- نظام أدوار وصلاحيات متقدم
- ملفات شخصية مفصلة للمستخدمين
- تتبع النشاط وسجلات الدخول
- إعدادات شخصية قابلة للتخصيص

### 🔔 نظام الإشعارات
- إشعارات فورية داخل النظام
- إشعارات عبر البريد الإلكتروني
- تذكيرات المواعيد والمهام
- إشعارات مخصصة حسب الدور

### 📱 تطبيق ويب تقدمي (PWA)
- يعمل على جميع الأجهزة (كمبيوتر، تابلت، هاتف)
- إمكانية التثبيت كتطبيق
- يعمل بدون إنترنت (وضع عدم الاتصال)
- تحديثات تلقائية

### 🔒 الأمان والحماية
- تشفير البيانات الحساسة
- مصادقة ثنائية العامل
- سجلات تدقيق شاملة
- نسخ احتياطية تلقائية

## متطلبات النظام

### الحد الأدنى
- Python 3.9+
- Django 4.2+
- SQL Server 2019+ أو PostgreSQL 13+
- Redis 6.0+
- 4GB RAM
- 20GB مساحة تخزين

### الموصى به
- Python 3.11+
- Django 4.2+
- SQL Server 2022 أو PostgreSQL 15+
- Redis 7.0+
- 8GB RAM
- 50GB مساحة تخزين
- SSD للأداء الأمثل

## التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-username/Lawyers_Office_Sys.git
cd Lawyers_Office_Sys
```

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
python manage.py migrate

# إنشاء مستخدم إداري
python manage.py createsuperuser

# تحميل البيانات الأولية (اختياري)
python manage.py loaddata fixtures/initial_data.json
```

### 5. جمع الملفات الثابتة
```bash
python manage.py collectstatic
```

### 6. تشغيل الخادم
```bash
python manage.py runserver
```

## النشر باستخدام Docker

### 1. إعداد متغيرات البيئة
```bash
cp .env.example .env
# قم بتعديل الملف .env حسب بيئتك
```

### 2. بناء وتشغيل الحاويات
```bash
# للتطوير
docker-compose -f docker-compose.dev.yml up -d

# للإنتاج
docker-compose up -d
```

### 3. تشغيل الترحيلات
```bash
docker-compose exec web python manage.py migrate
```

### 4. إنشاء مستخدم إداري
```bash
docker-compose exec web python manage.py createsuperuser
```

## الاستخدام

### الوصول للنظام
- **الموقع الرئيسي**: http://localhost:8000
- **لوحة الإدارة**: http://localhost:8000/admin/
- **واجهة برمجة التطبيقات**: http://localhost:8000/api/

### الحسابات الافتراضية
بعد تحميل البيانات الأولية:
- **المدير العام**: admin / admin123
- **محامي**: lawyer / lawyer123
- **سكرتير**: secretary / secretary123

## الاختبار

### تشغيل الاختبارات
```bash
# جميع الاختبارات
python manage.py test

# اختبارات تطبيق محدد
python manage.py test cases

# اختبارات مع تغطية الكود
coverage run --source='.' manage.py test
coverage report
coverage html
```

## الدعم والمساعدة

### الوثائق
- [دليل المستخدم](docs/user-guide.md)
- [دليل المطور](docs/developer-guide.md)
- [واجهة برمجة التطبيقات](docs/api-reference.md)

### الحصول على المساعدة
- [Issues على GitHub](https://github.com/your-username/Lawyers_Office_Sys/issues)
- [المنتدى](https://forum.your-domain.com)
- البريد الإلكتروني: <EMAIL>

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**ملاحظة**: هذا النظام في مرحلة التطوير النشط. يرجى الإبلاغ عن أي مشاكل أو اقتراحات عبر GitHub Issues.