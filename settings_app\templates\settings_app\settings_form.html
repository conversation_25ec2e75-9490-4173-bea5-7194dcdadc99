{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card p-4 mt-4 shadow rounded-4 border-0">
                <h4 class="mb-3 fw-bold"><i class="bi bi-gear text-primary me-2"></i>إعدادات النظام</h4>
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-success">{{ message }}</div>
                    {% endfor %}
                {% endif %}
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    {{ form.as_p }}
                    <button type="submit" class="btn btn-gradient-gold mt-2">حفظ الإعدادات</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
<style>
.btn-gradient-gold {
    background: linear-gradient(90deg, #ffd600 0%, #ffb300 100%);
    color: #222b45;
    border: none;
    font-weight: bold;
    transition: box-shadow 0.2s;
}
.btn-gradient-gold:hover {
    box-shadow: 0 2px 12px #ffd60055;
    color: #151a30;
}
</style>
{% endblock %} 