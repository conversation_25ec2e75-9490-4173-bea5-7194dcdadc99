# Generated by Django 5.0.14 on 2025-07-01 11:36

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entity_type', models.CharField(choices=[('case', 'قضية'), ('document', 'مستند'), ('appointment', 'موعد'), ('client', 'عميل'), ('user', 'مستخدم'), ('other', 'أخرى')], max_length=20)),
                ('entity_id', models.CharField(max_length=50)),
                ('action', models.CharField(choices=[('create', 'إضافة'), ('update', 'تعديل'), ('delete', 'حذف'), ('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('other', 'أخرى')], max_length=20)),
                ('description', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
    ]
