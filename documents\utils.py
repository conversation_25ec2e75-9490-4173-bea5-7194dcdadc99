import os
import uuid
import mimetypes
from PIL import Image
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.utils.text import slugify
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

# Allowed file types
ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
ALLOWED_DOCUMENT_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv'
]
ALLOWED_ARCHIVE_TYPES = ['application/zip', 'application/x-rar-compressed']

# Maximum file sizes (in bytes)
MAX_IMAGE_SIZE = 5 * 1024 * 1024  # 5MB
MAX_DOCUMENT_SIZE = 50 * 1024 * 1024  # 50MB
MAX_ARCHIVE_SIZE = 100 * 1024 * 1024  # 100MB

def validate_file(file):
    """
    Validate uploaded file type and size
    """
    # Get file type
    file_type, _ = mimetypes.guess_type(file.name)
    if not file_type:
        return False, "نوع الملف غير مدعوم"
    
    # Check file type
    if file_type in ALLOWED_IMAGE_TYPES:
        max_size = MAX_IMAGE_SIZE
        category = 'image'
    elif file_type in ALLOWED_DOCUMENT_TYPES:
        max_size = MAX_DOCUMENT_SIZE
        category = 'document'
    elif file_type in ALLOWED_ARCHIVE_TYPES:
        max_size = MAX_ARCHIVE_SIZE
        category = 'archive'
    else:
        return False, f"نوع الملف {file_type} غير مدعوم"
    
    # Check file size
    if file.size > max_size:
        max_size_mb = max_size / (1024 * 1024)
        return False, f"حجم الملف كبير جداً. الحد الأقصى {max_size_mb:.1f} ميجابايت"
    
    return True, category

def generate_unique_filename(original_filename):
    """
    Generate a unique filename while preserving the extension
    """
    name, ext = os.path.splitext(original_filename)
    # Clean the filename
    clean_name = slugify(name)
    # Generate unique identifier
    unique_id = str(uuid.uuid4())[:8]
    # Combine with timestamp
    timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
    
    return f"{clean_name}_{timestamp}_{unique_id}{ext}"

def get_file_upload_path(instance, filename):
    """
    Generate upload path for files based on the model and date
    """
    # Generate unique filename
    unique_filename = generate_unique_filename(filename)
    
    # Determine the upload directory based on the model
    if hasattr(instance, 'case') and instance.case:
        # Case-related document
        case_id = instance.case.id
        return f"documents/cases/{case_id}/{unique_filename}"
    elif hasattr(instance, 'client') and instance.client:
        # Client-related document
        client_id = instance.client.id
        return f"documents/clients/{client_id}/{unique_filename}"
    else:
        # General document
        return f"documents/general/{unique_filename}"

def create_thumbnail(image_file, size=(300, 300)):
    """
    Create a thumbnail for an image file
    """
    try:
        # Open the image
        image = Image.open(image_file)
        
        # Convert to RGB if necessary
        if image.mode in ('RGBA', 'LA', 'P'):
            image = image.convert('RGB')
        
        # Create thumbnail
        image.thumbnail(size, Image.Resampling.LANCZOS)
        
        # Save to memory
        from io import BytesIO
        thumb_io = BytesIO()
        image.save(thumb_io, format='JPEG', quality=85)
        thumb_io.seek(0)
        
        return ContentFile(thumb_io.read())
        
    except Exception as e:
        logger.error(f"Error creating thumbnail: {str(e)}")
        return None

def get_file_info(file_path):
    """
    Get detailed information about a file
    """
    try:
        if not default_storage.exists(file_path):
            return None
        
        # Get file stats
        file_size = default_storage.size(file_path)
        file_url = default_storage.url(file_path)
        
        # Get file type
        file_type, _ = mimetypes.guess_type(file_path)
        
        # Get file extension
        _, ext = os.path.splitext(file_path)
        
        return {
            'path': file_path,
            'url': file_url,
            'size': file_size,
            'size_human': format_file_size(file_size),
            'type': file_type,
            'extension': ext.lower(),
            'is_image': file_type in ALLOWED_IMAGE_TYPES if file_type else False,
            'is_document': file_type in ALLOWED_DOCUMENT_TYPES if file_type else False,
        }
        
    except Exception as e:
        logger.error(f"Error getting file info: {str(e)}")
        return None

def format_file_size(size_bytes):
    """
    Format file size in human readable format
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    
    return f"{s} {size_names[i]}"

def delete_file_safely(file_path):
    """
    Safely delete a file and its thumbnail if exists
    """
    try:
        if default_storage.exists(file_path):
            default_storage.delete(file_path)
            logger.info(f"File deleted: {file_path}")
            
            # Try to delete thumbnail if it exists
            thumb_path = get_thumbnail_path(file_path)
            if thumb_path and default_storage.exists(thumb_path):
                default_storage.delete(thumb_path)
                logger.info(f"Thumbnail deleted: {thumb_path}")
            
            return True
    except Exception as e:
        logger.error(f"Error deleting file {file_path}: {str(e)}")
        return False

def get_thumbnail_path(file_path):
    """
    Get the thumbnail path for a given file path
    """
    dir_path, filename = os.path.split(file_path)
    name, ext = os.path.splitext(filename)
    return os.path.join(dir_path, 'thumbnails', f"{name}_thumb.jpg")

def organize_files_by_date(files):
    """
    Organize files by upload date
    """
    organized = {}
    
    for file_obj in files:
        upload_date = file_obj.uploaded_at.date()
        date_key = upload_date.strftime('%Y-%m-%d')
        
        if date_key not in organized:
            organized[date_key] = []
        
        organized[date_key].append(file_obj)
    
    return organized

def get_file_statistics():
    """
    Get file storage statistics
    """
    from .models import Document
    
    try:
        total_files = Document.objects.count()
        
        # Calculate total size
        total_size = 0
        for doc in Document.objects.all():
            if doc.file and default_storage.exists(doc.file.name):
                total_size += default_storage.size(doc.file.name)
        
        # Count by type
        image_count = Document.objects.filter(
            file__iregex=r'\.(jpg|jpeg|png|gif|webp)$'
        ).count()
        
        pdf_count = Document.objects.filter(
            file__iregex=r'\.pdf$'
        ).count()
        
        doc_count = Document.objects.filter(
            file__iregex=r'\.(doc|docx|xls|xlsx)$'
        ).count()
        
        return {
            'total_files': total_files,
            'total_size': total_size,
            'total_size_human': format_file_size(total_size),
            'image_count': image_count,
            'pdf_count': pdf_count,
            'doc_count': doc_count,
            'other_count': total_files - image_count - pdf_count - doc_count,
        }
        
    except Exception as e:
        logger.error(f"Error getting file statistics: {str(e)}")
        return None

def cleanup_orphaned_files():
    """
    Clean up files that are not referenced in the database
    """
    from .models import Document
    
    try:
        # Get all file paths from database
        db_files = set()
        for doc in Document.objects.all():
            if doc.file:
                db_files.add(doc.file.name)
        
        # Get all files in storage
        storage_files = set()
        
        def collect_files(path):
            try:
                dirs, files = default_storage.listdir(path)
                for file in files:
                    storage_files.add(os.path.join(path, file))
                for dir in dirs:
                    collect_files(os.path.join(path, dir))
            except:
                pass
        
        collect_files('documents/')
        
        # Find orphaned files
        orphaned_files = storage_files - db_files
        
        # Delete orphaned files
        deleted_count = 0
        for file_path in orphaned_files:
            if delete_file_safely(file_path):
                deleted_count += 1
        
        logger.info(f"Cleaned up {deleted_count} orphaned files")
        return deleted_count
        
    except Exception as e:
        logger.error(f"Error cleaning up orphaned files: {str(e)}")
        return 0

def compress_image(image_file, quality=85, max_width=1920, max_height=1080):
    """
    Compress and resize an image file
    """
    try:
        image = Image.open(image_file)
        
        # Convert to RGB if necessary
        if image.mode in ('RGBA', 'LA', 'P'):
            image = image.convert('RGB')
        
        # Resize if too large
        if image.width > max_width or image.height > max_height:
            image.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
        
        # Save compressed version
        from io import BytesIO
        output = BytesIO()
        image.save(output, format='JPEG', quality=quality, optimize=True)
        output.seek(0)
        
        return ContentFile(output.read())
        
    except Exception as e:
        logger.error(f"Error compressing image: {str(e)}")
        return image_file
