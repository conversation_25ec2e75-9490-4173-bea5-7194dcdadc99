from django.urls import path, include
from .views import (
    AppointmentListView, AppointmentCreateView, AppointmentUpdateView, AppointmentDetailView, AppointmentDeleteView, AppointmentViewSet
)
from rest_framework.routers import DefaultRouter

app_name = 'appointments'

router = DefaultRouter()
router.register(r'api/appointments', AppointmentViewSet, basename='api-appointments')

urlpatterns = [
    path('', AppointmentListView.as_view(), name='list'),
    path('add/', AppointmentCreateView.as_view(), name='add'),
    path('<int:pk>/edit/', AppointmentUpdateView.as_view(), name='edit'),
    path('<int:pk>/', AppointmentDetailView.as_view(), name='detail'),
    path('<int:pk>/delete/', AppointmentDeleteView.as_view(), name='delete'),
] + router.urls 