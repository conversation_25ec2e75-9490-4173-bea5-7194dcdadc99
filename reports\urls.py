from django.urls import path
from .views import (
    reports_home, cases_report, cases_report_pdf, cases_report_excel,
    appointments_report, appointments_report_pdf, appointments_report_excel,
    documents_report, documents_report_pdf, documents_report_excel,
    ReportAccountsSummaryView, accounts_summary_excel, accounts_summary_pdf
)

app_name = 'reports'

urlpatterns = [
    path('', reports_home, name='home'),
    path('cases/', cases_report, name='cases_report'),
    path('cases/pdf/', cases_report_pdf, name='cases_report_pdf'),
    path('cases/excel/', cases_report_excel, name='cases_report_excel'),
    path('appointments/', appointments_report, name='appointments_report'),
    path('appointments/pdf/', appointments_report_pdf, name='appointments_report_pdf'),
    path('appointments/excel/', appointments_report_excel, name='appointments_report_excel'),
    path('documents/', documents_report, name='documents_report'),
    path('documents/pdf/', documents_report_pdf, name='documents_report_pdf'),
    path('documents/excel/', documents_report_excel, name='documents_report_excel'),
]

urlpatterns += [
    path('accounts/', ReportAccountsSummaryView.as_view(), name='accounts_summary'),
    path('accounts/excel/', accounts_summary_excel, name='accounts_summary_excel'),
    path('accounts/pdf/', accounts_summary_pdf, name='accounts_summary_pdf'),
] 