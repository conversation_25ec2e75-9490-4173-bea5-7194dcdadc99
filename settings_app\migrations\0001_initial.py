# Generated by Django 5.0.14 on 2025-07-01 10:44

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='OfficeSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('office_name', models.Char<PERSON>ield(max_length=255, verbose_name='اسم المكتب')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='settings/', verbose_name='الشعار')),
                ('address', models.CharField(blank=True, max_length=255, null=True, verbose_name='العنوان')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
            ],
        ),
    ]
