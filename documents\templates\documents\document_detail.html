{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-md-7 col-lg-6">
            <div class="card shadow rounded-4 border-0 p-4">
                <h4 class="mb-3 fw-bold"><i class="bi bi-file-earmark-text text-primary me-2"></i>تفاصيل المستند</h4>
                <ul class="list-group list-group-flush mb-3">
                    <li class="list-group-item">اسم المستند: <strong>{{ document.name }}</strong></li>
                    <li class="list-group-item">القضية: <strong>{{ document.case }}</strong></li>
                    <li class="list-group-item">العميل: <strong>{{ document.client }}</strong></li>
                    <li class="list-group-item">تاريخ الرفع: <strong>{{ document.uploaded_at|date:'Y-m-d H:i' }}</strong></li>
                    <li class="list-group-item">ملاحظات: <strong>{{ document.notes }}</strong></li>
                    <li class="list-group-item">الملف:
                        {% if document.file %}
                            <a href="{{ document.file.url }}" class="btn btn-sm btn-secondary ms-2" download>تحميل الملف</a>
                        {% else %}-{% endif %}
                    </li>
                </ul>
                <div class="d-flex justify-content-between gap-2">
                    <a href="{% url 'documents:edit' document.pk %}" class="btn btn-outline-primary flex-fill"><i class="bi bi-pencil-square"></i> تعديل</a>
                    <a href="{% url 'documents:delete' document.pk %}" class="btn btn-outline-danger flex-fill"><i class="bi bi-trash"></i> حذف</a>
                    <a href="{% url 'documents:list' %}" class="btn btn-link flex-fill"><i class="bi bi-arrow-right-circle"></i> العودة للقائمة</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
{% endblock %} 