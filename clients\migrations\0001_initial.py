# Generated by Django 5.0.14 on 2025-07-01 10:44

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Client',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.Char<PERSON>ield(max_length=255, verbose_name='الاسم الكامل')),
                ('national_id', models.CharField(max_length=50, verbose_name='رقم الهوية/جواز السفر')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.Char<PERSON>ield(blank=True, max_length=255, null=True, verbose_name='العنوان')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
            ],
        ),
    ]
