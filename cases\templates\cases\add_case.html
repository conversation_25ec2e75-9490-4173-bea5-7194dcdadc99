{% extends 'base.html' %}
{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg animate__animated animate__fadeIn rounded-3 overflow-hidden">
                <div class="card-header bg-primary text-white py-3">
                    <h4 class="mb-0 fw-bold">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة قضية جديدة
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- حقل نوع القضية -->
                        <div class="mb-3 animate__animated animate__fadeInUp">
                            <label class="form-label fw-bold">
                                نوع القضية <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" name="case_type" required>
                                <option value="">-- اختر نوع القضية --</option>
                                <option value="جنح">جنح</option>
                                <option value="جنايات">جنايات</option>
                                <option value="أسرة">أسرة</option>
                                <option value="أحوال شخصية">أحوال شخصية</option>
                                <option value="تجارية">تجارية</option>
                                <option value="إدارية">إدارية</option>
                            </select>
                        </div>

                        <!-- حقل المحكمة -->
                        <div class="mb-3 animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                            <label class="form-label fw-bold">
                                المحكمة <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" name="court" required>
                                <option value="">-- اختر المحكمة --</option>
                                {% for court in courts %}
                                    <option value="{{ court.id }}">{{ court.name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- حقل صفة العميل -->
                        <div class="mb-3 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                            <label class="form-label fw-bold">
                                صفة العميل <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" name="client_role" required>
                                <option value="">-- اختر الصفة --</option>
                                <option value="مدعي">مدعي</option>
                                <option value="مدعى عليه">مدعى عليه</option>
                                <option value="متهم">متهم</option>
                                <option value="مدع">مدع</option>
                                <option value="مشتكى عليه">مشتكى عليه</option>
                            </select>
                        </div>

                        {% for field in form %}
                        <div class="mb-3 animate__animated animate__fadeInUp" style="animation-delay: {{ forloop.counter|add:'0.3' }}s">
                            <label for="{{ field.id_for_label }}" class="form-label fw-bold">
                                {{ field.label }}
                                {% if field.field.required %}<span class="text-danger">*</span>{% endif %}
                            </label>
                            {{ field }}
                            {% if field.help_text %}
                                <small class="text-muted">{{ field.help_text }}</small>
                            {% endif %}
                            {% for error in field.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endfor %}

                        <div class="d-flex justify-content-between mt-4">
                            <button type="submit" class="btn btn-primary px-4 py-2 rounded-pill shadow-sm">
                                <i class="bi bi-check-circle me-2"></i> حفظ القضية
                            </button>
                            <a href="{% url 'cases:case_list' %}" class="btn btn-outline-secondary px-4 py-2 rounded-pill">
                                <i class="bi bi-arrow-left-circle me-2"></i> العودة للقائمة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
<style>
.card {
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.form-control, .form-select {
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.invalid-feedback {
    animation: fadeIn 0.3s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>
{% endblock %}
