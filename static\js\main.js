// Main JavaScript for Lawyers Office System

// Global Variables
let currentTheme = localStorage.getItem('theme') || 'light';
let sidebarState = localStorage.getItem('sidebarState') || 'open';

// Initialize on DOM Load
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    initializeSidebar();
    initializeTooltips();
    initializeModals();
    initializeFormValidation();
    initializeDataTables();
    initializeNotifications();
    initializeMobileMenu();
});

// Theme Management
function initializeTheme() {
    document.body.setAttribute('data-theme', currentTheme);
    updateThemeIcon();
}

function toggleTheme() {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    document.body.setAttribute('data-theme', currentTheme);
    localStorage.setItem('theme', currentTheme);
    updateThemeIcon();
    
    // Animate theme transition
    document.body.style.transition = 'all 0.3s ease';
    setTimeout(() => {
        document.body.style.transition = '';
    }, 300);
}

function updateThemeIcon() {
    const icon = document.getElementById('themeIcon');
    if (icon) {
        if (currentTheme === 'dark') {
            icon.innerHTML = '<path d="M12.293 9.293a1 1 0 0 1 1.414 1.414A7 7 0 1 1 6 0a1 1 0 0 1 0 2 5 5 0 1 0 5 5 1 1 0 0 1 1.293 2.293z"/>';
        } else {
            icon.innerHTML = '<path d="M6 0a7 7 0 0 0 0 14c3.866 0 7-3.134 7-7 0-.256-.012-.51-.035-.762A6.978 6.978 0 0 1 6 0z"/>';
        }
    }
}

// Sidebar Management
function initializeSidebar() {
    const sidebar = document.querySelector('.sidebar');
    if (window.innerWidth <= 991) {
        sidebar.classList.remove('show');
    }
}

function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    sidebar.classList.toggle('show');
}

// Mobile Menu
function initializeMobileMenu() {
    // Add mobile menu toggle button if not exists
    const navbar = document.querySelector('.navbar .container-fluid');
    if (navbar && window.innerWidth <= 991) {
        const existingToggle = document.querySelector('.mobile-menu-toggle');
        if (!existingToggle) {
            const toggleButton = document.createElement('button');
            toggleButton.className = 'btn btn-outline-primary mobile-menu-toggle d-lg-none';
            toggleButton.innerHTML = '<i class="bi bi-list"></i>';
            toggleButton.onclick = toggleSidebar;
            navbar.insertBefore(toggleButton, navbar.firstChild);
        }
    }
}

// Initialize Bootstrap Tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Initialize Bootstrap Modals
function initializeModals() {
    // Auto-focus first input in modals
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('shown.bs.modal', function () {
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) {
                firstInput.focus();
            }
        });
    });
}

// Form Validation Enhancement
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                
                // Focus on first invalid field
                const firstInvalid = form.querySelector(':invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                }
            }
            form.classList.add('was-validated');
        });
    });
}

// DataTables Enhancement
function initializeDataTables() {
    if (typeof $.fn.DataTable !== 'undefined') {
        $('.data-table').DataTable({
            responsive: true,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
            },
            pageLength: 25,
            order: [[0, 'desc']],
            columnDefs: [
                { orderable: false, targets: 'no-sort' }
            ]
        });
    }
}

// Notification System
function initializeNotifications() {
    // Check for new notifications every 30 seconds
    setInterval(checkNotifications, 30000);
}

function checkNotifications() {
    fetch('/notifications/api/unread-count/')
        .then(response => response.json())
        .then(data => {
            const badge = document.getElementById('notif-count');
            if (badge) {
                badge.textContent = data.count;
                badge.style.display = data.count > 0 ? 'inline' : 'none';
            }
        })
        .catch(error => console.log('Notification check failed:', error));
}

function markNotificationAsRead(notificationId) {
    fetch(`/notifications/api/mark-read/${notificationId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            checkNotifications();
        }
    })
    .catch(error => console.log('Mark as read failed:', error));
}

// Utility Functions
function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

// Loading State Management
function showLoading(element) {
    if (element) {
        element.disabled = true;
        const originalText = element.textContent;
        element.innerHTML = '<span class="loading-spinner me-2"></span>جاري التحميل...';
        element.dataset.originalText = originalText;
    }
}

function hideLoading(element) {
    if (element && element.dataset.originalText) {
        element.disabled = false;
        element.innerHTML = element.dataset.originalText;
        delete element.dataset.originalText;
    }
}

// File Upload Enhancement
function initializeFileUpload() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const fileName = this.files[0]?.name || 'لم يتم اختيار ملف';
            const label = this.nextElementSibling;
            if (label && label.classList.contains('form-label')) {
                label.textContent = fileName;
            }
        });
    });
}

// Search Enhancement
function initializeSearch() {
    const searchInputs = document.querySelectorAll('.search-input');
    searchInputs.forEach(input => {
        let timeout;
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                performSearch(this.value);
            }, 300);
        });
    });
}

function performSearch(query) {
    // Implement search functionality
    console.log('Searching for:', query);
}

// Export Functions
function exportToExcel(tableId) {
    // Implement Excel export
    showAlert('جاري تصدير البيانات...', 'info');
}

function exportToPDF(elementId) {
    // Implement PDF export
    showAlert('جاري إنشاء ملف PDF...', 'info');
}

// Window Resize Handler
window.addEventListener('resize', function() {
    initializeMobileMenu();
    
    // Hide sidebar on mobile when resizing to mobile view
    if (window.innerWidth <= 991) {
        const sidebar = document.querySelector('.sidebar');
        sidebar.classList.remove('show');
    }
});

// Keyboard Shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + K for search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.focus();
        }
    }
    
    // Escape to close modals/sidebar
    if (e.key === 'Escape') {
        const sidebar = document.querySelector('.sidebar.show');
        if (sidebar) {
            sidebar.classList.remove('show');
        }
    }
});

// Print Functionality
function printPage() {
    window.print();
}

// Auto-save for forms (optional)
function initializeAutoSave() {
    const forms = document.querySelectorAll('.auto-save');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                // Implement auto-save logic
                console.log('Auto-saving form data...');
            });
        });
    });
}
