"""
Comprehensive test suite for Lawyers Office System views
"""

from django.test import TestCase, Client as TestClient
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from datetime import timed<PERSON>ta
import json

from cases.models import Case
from clients.models import Client
from appointments.models import Appointment
from documents.models import Document, DocumentCategory
from users.models import UserRole

User = get_user_model()

class BaseViewTest(TestCase):
    """Base test class with common setup"""
    
    def setUp(self):
        # Create test role
        self.role = UserRole.objects.create(
            name='lawyer',
            display_name='محامي',
            description='محامي في المكتب'
        )
        
        # Create test users
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='testpass123',
            is_staff=True,
            is_superuser=True,
            role=self.role
        )
        
        self.lawyer_user = User.objects.create_user(
            username='lawyer',
            email='<EMAIL>',
            password='testpass123',
            is_staff=True,
            role=self.role
        )
        
        self.regular_user = User.objects.create_user(
            username='user',
            email='<EMAIL>',
            password='testpass123',
            role=self.role
        )
        
        # Create test client
        self.test_client = Client.objects.create(
            full_name='محمد أحمد',
            national_id='1234567890',
            phone='0501234567',
            email='<EMAIL>'
        )
        
        # Create test case
        self.test_case = Case.objects.create(
            case_number='2024/001',
            title='قضية تجريبية',
            description='وصف القضية التجريبية',
            client=self.test_client,
            lawyer=self.lawyer_user,
            start_date=timezone.now().date(),
            status='open'
        )
        
        # Create test appointment
        self.test_appointment = Appointment.objects.create(
            title='استشارة قانونية',
            client=self.test_client,
            lawyer=self.lawyer_user,
            datetime=timezone.now() + timedelta(days=1),
            location='المكتب الرئيسي'
        )
        
        # Create test document category
        self.doc_category = DocumentCategory.objects.create(
            name='عقود',
            description='فئة العقود'
        )
        
        # Create test document
        self.test_document = Document.objects.create(
            name='مستند تجريبي',
            description='وصف المستند',
            client=self.test_client,
            case=self.test_case,
            category=self.doc_category,
            uploaded_by=self.lawyer_user,
            document_type='contract'
        )
        
        self.client = TestClient()

class DashboardViewTest(BaseViewTest):
    """Test dashboard views"""
    
    def test_dashboard_requires_login(self):
        """Test that dashboard requires authentication"""
        response = self.client.get(reverse('dashboard:dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
    
    def test_dashboard_authenticated_access(self):
        """Test dashboard access for authenticated users"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('dashboard:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'لوحة التحكم')
    
    def test_dashboard_context_data(self):
        """Test dashboard context contains required data"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('dashboard:dashboard'))
        
        self.assertIn('cases_count', response.context)
        self.assertIn('clients_count', response.context)
        self.assertIn('appointments_count', response.context)
        self.assertIn('documents_count', response.context)

class CaseViewTest(BaseViewTest):
    """Test case views"""
    
    def test_case_list_view(self):
        """Test case list view"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('cases:case_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.test_case.title)
    
    def test_case_detail_view(self):
        """Test case detail view"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('cases:case_detail', kwargs={'pk': self.test_case.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.test_case.title)
        self.assertContains(response, self.test_case.case_number)
    
    def test_case_create_view_get(self):
        """Test case create view GET request"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('cases:case_create'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'إضافة قضية جديدة')
    
    def test_case_create_view_post(self):
        """Test case create view POST request"""
        self.client.login(username='lawyer', password='testpass123')
        
        data = {
            'case_number': '2024/002',
            'title': 'قضية جديدة',
            'description': 'وصف القضية الجديدة',
            'client': self.test_client.pk,
            'lawyer': self.lawyer_user.pk,
            'start_date': timezone.now().date(),
            'status': 'open'
        }
        
        response = self.client.post(reverse('cases:case_create'), data)
        self.assertEqual(response.status_code, 302)  # Redirect after successful creation
        
        # Check if case was created
        self.assertTrue(Case.objects.filter(case_number='2024/002').exists())
    
    def test_case_update_view(self):
        """Test case update view"""
        self.client.login(username='lawyer', password='testpass123')
        
        data = {
            'case_number': self.test_case.case_number,
            'title': 'قضية محدثة',
            'description': self.test_case.description,
            'client': self.test_client.pk,
            'lawyer': self.lawyer_user.pk,
            'start_date': self.test_case.start_date,
            'status': 'closed'
        }
        
        response = self.client.post(
            reverse('cases:case_update', kwargs={'pk': self.test_case.pk}), 
            data
        )
        self.assertEqual(response.status_code, 302)
        
        # Check if case was updated
        updated_case = Case.objects.get(pk=self.test_case.pk)
        self.assertEqual(updated_case.title, 'قضية محدثة')
        self.assertEqual(updated_case.status, 'closed')

class ClientViewTest(BaseViewTest):
    """Test client views"""
    
    def test_client_list_view(self):
        """Test client list view"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('clients:client_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.test_client.full_name)
    
    def test_client_detail_view(self):
        """Test client detail view"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('clients:client_detail', kwargs={'pk': self.test_client.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.test_client.full_name)
        self.assertContains(response, self.test_client.national_id)
    
    def test_client_create_view(self):
        """Test client create view"""
        self.client.login(username='lawyer', password='testpass123')
        
        data = {
            'full_name': 'أحمد محمد',
            'national_id': '0987654321',
            'phone': '0501234568',
            'email': '<EMAIL>'
        }
        
        response = self.client.post(reverse('clients:client_create'), data)
        self.assertEqual(response.status_code, 302)
        
        # Check if client was created
        self.assertTrue(Client.objects.filter(national_id='0987654321').exists())

class AppointmentViewTest(BaseViewTest):
    """Test appointment views"""
    
    def test_appointment_list_view(self):
        """Test appointment list view"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('appointments:appointment_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.test_appointment.title)
    
    def test_appointment_detail_view(self):
        """Test appointment detail view"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('appointments:appointment_detail', kwargs={'pk': self.test_appointment.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.test_appointment.title)
    
    def test_appointment_create_view(self):
        """Test appointment create view"""
        self.client.login(username='lawyer', password='testpass123')
        
        future_datetime = timezone.now() + timedelta(days=2)
        data = {
            'title': 'موعد جديد',
            'client': self.test_client.pk,
            'lawyer': self.lawyer_user.pk,
            'datetime': future_datetime.strftime('%Y-%m-%d %H:%M'),
            'location': 'مكتب فرعي'
        }
        
        response = self.client.post(reverse('appointments:appointment_create'), data)
        # Note: This might fail due to form validation, adjust as needed
        # self.assertEqual(response.status_code, 302)

class DocumentViewTest(BaseViewTest):
    """Test document views"""
    
    def test_document_list_view(self):
        """Test document list view"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('documents:document_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.test_document.name)
    
    def test_document_detail_view(self):
        """Test document detail view"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('documents:document_detail', kwargs={'pk': self.test_document.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.test_document.name)

class SearchViewTest(BaseViewTest):
    """Test search views"""
    
    def test_advanced_search_view(self):
        """Test advanced search view"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('search:advanced_search'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'البحث المتقدم')
    
    def test_search_with_query(self):
        """Test search with query parameter"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('search:advanced_search'), {'q': 'محمد'})
        self.assertEqual(response.status_code, 200)
    
    def test_search_api(self):
        """Test search API endpoint"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('search:search_api'), {'q': 'محمد'})
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertIn('results', data)

class UserViewTest(BaseViewTest):
    """Test user management views"""
    
    def test_user_list_view_admin_access(self):
        """Test user list view for admin users"""
        self.client.login(username='admin', password='testpass123')
        response = self.client.get(reverse('users:user_list'))
        self.assertEqual(response.status_code, 200)
    
    def test_user_list_view_regular_user_denied(self):
        """Test user list view denied for regular users"""
        self.client.login(username='user', password='testpass123')
        response = self.client.get(reverse('users:user_list'))
        # Should be redirected or forbidden
        self.assertIn(response.status_code, [302, 403])
    
    def test_user_profile_view(self):
        """Test user profile view"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('users:profile'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.lawyer_user.username)

class PermissionTest(BaseViewTest):
    """Test view permissions"""
    
    def test_anonymous_user_redirected(self):
        """Test that anonymous users are redirected to login"""
        protected_urls = [
            reverse('dashboard:dashboard'),
            reverse('cases:case_list'),
            reverse('clients:client_list'),
            reverse('appointments:appointment_list'),
            reverse('documents:document_list'),
        ]
        
        for url in protected_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)
    
    def test_staff_required_views(self):
        """Test views that require staff permissions"""
        staff_required_urls = [
            reverse('users:user_list'),
            reverse('reports:reports_home'),
        ]
        
        # Test with regular user (should be denied)
        self.client.login(username='user', password='testpass123')
        for url in staff_required_urls:
            response = self.client.get(url)
            self.assertIn(response.status_code, [302, 403])
        
        # Test with staff user (should be allowed)
        self.client.login(username='lawyer', password='testpass123')
        for url in staff_required_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)

class APIViewTest(BaseViewTest):
    """Test API endpoints"""
    
    def test_dashboard_api_stats(self):
        """Test dashboard API stats endpoint"""
        self.client.login(username='lawyer', password='testpass123')
        response = self.client.get(reverse('dashboard:api_stats'))
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertIn('cases', data)
        self.assertIn('clients', data)
        self.assertIn('appointments', data)
        self.assertIn('documents', data)
    
    def test_user_search_api(self):
        """Test user search API endpoint"""
        self.client.login(username='admin', password='testpass123')
        response = self.client.get(reverse('users:user_search_api'), {'q': 'lawyer'})
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertIn('users', data)

class FormValidationTest(BaseViewTest):
    """Test form validation"""
    
    def test_case_form_validation(self):
        """Test case form validation"""
        self.client.login(username='lawyer', password='testpass123')
        
        # Test with invalid data
        data = {
            'case_number': '',  # Required field
            'title': '',  # Required field
            'client': '',  # Required field
        }
        
        response = self.client.post(reverse('cases:case_create'), data)
        self.assertEqual(response.status_code, 200)  # Form should be redisplayed with errors
        self.assertFormError(response, 'form', 'case_number', 'This field is required.')
    
    def test_client_form_validation(self):
        """Test client form validation"""
        self.client.login(username='lawyer', password='testpass123')
        
        # Test with duplicate national_id
        data = {
            'full_name': 'عميل جديد',
            'national_id': self.test_client.national_id,  # Duplicate
            'phone': '0501234569'
        }
        
        response = self.client.post(reverse('clients:client_create'), data)
        self.assertEqual(response.status_code, 200)  # Form should be redisplayed with errors
