{% extends 'base.html' %}

{% block title %}الملف الشخصي - {{ user.get_full_name|default:user.username }}{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active" aria-current="page">الملف الشخصي</li>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Profile Sidebar -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <!-- Profile Picture -->
                    <div class="mb-4">
                        <div class="position-relative d-inline-block">
                            {% if user.profile_picture %}
                                <img src="{{ user.profile_picture.url }}" alt="الصورة الشخصية" 
                                     class="rounded-circle border border-3 border-light shadow" 
                                     style="width: 120px; height: 120px; object-fit: cover;">
                            {% else %}
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center border border-3 border-light shadow" 
                                     style="width: 120px; height: 120px;">
                                    <i class="bi bi-person-fill text-white" style="font-size: 3rem;"></i>
                                </div>
                            {% endif %}
                            <button class="btn btn-sm btn-primary rounded-circle position-absolute bottom-0 end-0" 
                                    data-bs-toggle="modal" data-bs-target="#profilePictureModal">
                                <i class="bi bi-camera"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- User Info -->
                    <h4 class="mb-2">{{ user.get_full_name|default:user.username }}</h4>
                    <p class="text-muted mb-3">{{ user.email }}</p>
                    
                    <!-- User Stats -->
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border-end">
                                <h5 class="mb-0 text-primary">{{ user_stats.cases_count }}</h5>
                                <small class="text-muted">القضايا</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-end">
                                <h5 class="mb-0 text-success">{{ user_stats.clients_count }}</h5>
                                <small class="text-muted">العملاء</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <h5 class="mb-0 text-warning">{{ user_stats.appointments_count }}</h5>
                            <small class="text-muted">المواعيد</small>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="mt-4">
                        <button class="btn btn-primary w-100 mb-2" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                            <i class="bi bi-pencil me-2"></i>
                            تعديل الملف الشخصي
                        </button>
                        <button class="btn btn-outline-secondary w-100" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            <i class="bi bi-key me-2"></i>
                            تغيير كلمة المرور
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Quick Info Card -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-transparent border-0">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        معلومات إضافية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">تاريخ الانضمام:</span>
                        <span>{{ user.date_joined|date:"j F Y" }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">آخر تسجيل دخول:</span>
                        <span>{{ user.last_login|date:"j F Y H:i"|default:"لم يسجل دخول بعد" }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">الحالة:</span>
                        <span class="badge bg-{% if user.is_active %}success{% else %}danger{% endif %}">
                            {% if user.is_active %}نشط{% else %}غير نشط{% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">الصلاحيات:</span>
                        <span class="badge bg-{% if user.is_superuser %}danger{% elif user.is_staff %}warning{% else %}primary{% endif %}">
                            {% if user.is_superuser %}مدير عام{% elif user.is_staff %}موظف{% else %}مستخدم{% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Recent Activity -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>
                        النشاط الأخير
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_activities %}
                        <div class="timeline">
                            {% for activity in recent_activities %}
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">{{ activity.title }}</h6>
                                    <p class="text-muted mb-1">{{ activity.description }}</p>
                                    <small class="text-muted">{{ activity.created_at|timesince }} مضت</small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4 text-muted">
                            <i class="bi bi-clock display-6 d-block mb-3 opacity-50"></i>
                            <p class="mb-0">لا يوجد نشاط حديث</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Preferences -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-gear me-2"></i>
                        التفضيلات
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'profile_preferences' %}">
                        {% csrf_token %}
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">اللغة المفضلة</label>
                                <select name="language" class="form-select">
                                    <option value="ar" {% if user.preferences.language == 'ar' %}selected{% endif %}>العربية</option>
                                    <option value="en" {% if user.preferences.language == 'en' %}selected{% endif %}>English</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">المنطقة الزمنية</label>
                                <select name="timezone" class="form-select">
                                    <option value="Asia/Riyadh" {% if user.preferences.timezone == 'Asia/Riyadh' %}selected{% endif %}>الرياض</option>
                                    <option value="Asia/Dubai" {% if user.preferences.timezone == 'Asia/Dubai' %}selected{% endif %}>دبي</option>
                                    <option value="Africa/Cairo" {% if user.preferences.timezone == 'Africa/Cairo' %}selected{% endif %}>القاهرة</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="email_notifications" 
                                           {% if user.preferences.email_notifications %}checked{% endif %}>
                                    <label class="form-check-label">
                                        تلقي الإشعارات عبر البريد الإلكتروني
                                    </label>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="dark_mode" 
                                           {% if user.preferences.dark_mode %}checked{% endif %}>
                                    <label class="form-check-label">
                                        الوضع الليلي
                                    </label>
                                </div>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check me-2"></i>
                                    حفظ التفضيلات
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Profile Modal -->
<div class="modal fade" id="editProfileModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الملف الشخصي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'edit_profile' %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label">الاسم الأول</label>
                        <input type="text" name="first_name" class="form-control" value="{{ user.first_name }}">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الاسم الأخير</label>
                        <input type="text" name="last_name" class="form-control" value="{{ user.last_name }}">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" name="email" class="form-control" value="{{ user.email }}">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تغيير كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'change_password' %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور الحالية</label>
                        <input type="password" name="old_password" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" name="new_password1" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">تأكيد كلمة المرور الجديدة</label>
                        <input type="password" name="new_password2" class="form-control" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تغيير كلمة المرور</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -1.5rem;
    top: 1.5rem;
    width: 2px;
    height: calc(100% + 1rem);
    background-color: #e9ecef;
}

.timeline-marker {
    position: absolute;
    left: -1.75rem;
    top: 0.25rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 3px solid #0d6efd;
}
</style>
{% endblock %}
