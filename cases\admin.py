from django.contrib import admin
from .models import Case, Hearing, HearingDocument, CourtDecision, HearingAction, HearingImportantDate, HearingReminder, Fee

class HearingDocumentInline(admin.TabularInline):
    model = HearingDocument
    extra = 1

class CourtDecisionInline(admin.StackedInline):
    model = CourtDecision
    extra = 0
    max_num = 1

class HearingActionInline(admin.TabularInline):
    model = HearingAction
    extra = 1

class HearingImportantDateInline(admin.TabularInline):
    model = HearingImportantDate
    extra = 1

class HearingReminderInline(admin.TabularInline):
    model = HearingReminder
    extra = 1

class HearingAdmin(admin.ModelAdmin):
    inlines = [HearingDocumentInline, CourtDecisionInline, HearingActionInline, HearingImportantDateInline, HearingReminderInline]
    list_display = ('case', 'date', 'location')
    list_filter = ('case',)
    search_fields = ('description', 'location')

class HearingInline(admin.TabularInline):
    model = Hearing
    extra = 1

class CaseAdmin(admin.ModelAdmin):
    inlines = [HearingInline]
    list_display = ('case_number', 'title', 'client', 'lawyer', 'start_date', 'end_date', 'status', 'case_type')
    search_fields = ('case_number', 'title', 'client__name')
    list_filter = ('status', 'case_type', 'lawyer', 'client')

class FeeAdmin(admin.ModelAdmin):
    list_display = ('case', 'amount', 'fee_type', 'due_date', 'is_paid', 'paid_date')
    list_filter = ('fee_type', 'is_paid', 'case')
    search_fields = ('case__case_number', 'case__title', 'note')

admin.site.register(Case, CaseAdmin)
admin.site.register(Hearing, HearingAdmin)
admin.site.register(HearingDocument)
admin.site.register(CourtDecision)
admin.site.register(HearingAction)
admin.site.register(HearingImportantDate)
admin.site.register(HearingReminder)
admin.site.register(Fee, FeeAdmin)

# Register your models here.
