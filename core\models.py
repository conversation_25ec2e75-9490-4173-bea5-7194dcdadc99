# models.py
from django.db import models
from django.conf import settings

# --- Enums using TextChoices for better readability ---

class CaseStatus(models.TextChoices):
    OPEN = 'OPEN', 'مفتوحة'
    IN_PROGRESS = 'IN_PROGRESS', 'قيد التنفيذ'
    PENDING = 'PENDING', 'معلقة'
    CLOSED = 'CLOSED', 'مغلقة'

class InvoiceStatus(models.TextChoices):
    UNPAID = 'UNPAID', 'غير مدفوعة'
    PAID = 'PAID', 'مدفوعة'
    OVERDUE = 'OVERDUE', 'متأخرة'

class TaskStatus(models.TextChoices):
    TODO = 'TODO', 'للقيام به'
    IN_PROGRESS = 'IN_PROGRESS', 'قيد التنفيذ'
    DONE = 'DONE', 'مكتملة'

# --- Core Models ---

class Lawyer(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, primary_key=True, related_name='lawyer_profile')
    phone = models.CharField(max_length=20, blank=True, null=True)
    specialization = models.CharField(max_length=100)

    def __str__(self):
        return self.user.get_full_name() or self.user.username

class Client(models.Model):
    # A client might not have a system login, so the user link is optional (nullable)
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, blank=True, null=True, related_name='client_profile')
    name = models.CharField(max_length=255)
    phone = models.CharField(max_length=20)
    email = models.EmailField(unique=True)
    address = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

class Case(models.Model):
    title = models.CharField(max_length=255)
    description = models.TextField()
    start_date = models.DateField()
    status = models.CharField(max_length=20, choices=CaseStatus.choices, default=CaseStatus.OPEN)
    
    # Many-to-many relationships
    lawyers = models.ManyToManyField(Lawyer, related_name='cases')
    clients = models.ManyToManyField(Client, related_name='cases')

    def __str__(self):
        return self.title

class Hearing(models.Model):
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='hearings')
    datetime = models.DateTimeField()
    location = models.CharField(max_length=255)
    outcome = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"Hearing for {self.case.title} on {self.datetime.strftime('%Y-%m-%d')}"

class Appointment(models.Model):
    lawyer = models.ForeignKey(Lawyer, on_delete=models.CASCADE, related_name='appointments')
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='appointments')
    case = models.ForeignKey(Case, on_delete=models.SET_NULL, blank=True, null=True, related_name='appointments')
    start_datetime = models.DateTimeField()
    end_datetime = models.DateTimeField()
    location = models.CharField(max_length=255)
    notes = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"Appointment: {self.lawyer} with {self.client}"

class Document(models.Model):
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='documents')
    uploaded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='uploaded_documents')
    file = models.FileField(upload_to='case_documents/') # Django will handle file paths
    file_name = models.CharField(max_length=255)
    uploaded_date = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.file_name

class CaseNote(models.Model):
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='notes')
    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='case_notes')
    note = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Note on {self.case.title} by {self.author.username}"

class Task(models.Model):
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='tasks')
    assigned_to = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='tasks')
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    due_date = models.DateField()
    status = models.CharField(max_length=20, choices=TaskStatus.choices, default=TaskStatus.TODO)

    def __str__(self):
        return self.title

# --- Financial Models ---

class Invoice(models.Model):
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='invoices')
    date_issued = models.DateField(auto_now_add=True)
    date_due = models.DateField()
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=InvoiceStatus.choices, default=InvoiceStatus.UNPAID)

    def __str__(self):
        return f"Invoice #{self.id} for {self.case.title}"

class Payment(models.Model):
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='payments')
    payment_date = models.DateField()
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=50, default='Bank Transfer')

    def __str__(self):
        return f"Payment of {self.amount_paid} for Invoice #{self.invoice.id}"

class TimeEntry(models.Model):
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='time_entries')
    lawyer = models.ForeignKey(Lawyer, on_delete=models.CASCADE, related_name='time_entries')
    invoice = models.ForeignKey(Invoice, on_delete=models.SET_NULL, blank=True, null=True, related_name='time_entries')
    date = models.DateField()
    hours_worked = models.DecimalField(max_digits=5, decimal_places=2)
    description = models.TextField()
    is_billed = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.hours_worked} hours by {self.lawyer} on {self.case.title}"

class Expense(models.Model):
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='expenses')
    invoice = models.ForeignKey(Invoice, on_delete=models.SET_NULL, blank=True, null=True, related_name='expenses')
    date = models.DateField()
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    description = models.TextField()
    is_billed = models.BooleanField(default=False)

    def __str__(self):
        return f"Expense of {self.amount} for {self.case.title}"