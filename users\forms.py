from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm, PasswordChangeForm
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from .models import UserRole
import re

User = get_user_model()

class CustomUserCreationForm(UserCreationForm):
    """Enhanced user creation form"""
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'first_name', 'last_name', 'phone', 'mobile',
            'national_id', 'date_of_birth', 'gender', 'address', 'role',
            'department', 'position', 'employee_id', 'hire_date'
        ]
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المستخدم'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني'
            }),
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الأول'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الأخير'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهاتف'
            }),
            'mobile': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الجوال'
            }),
            'national_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهوية'
            }),
            'date_of_birth': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'gender': forms.Select(attrs={
                'class': 'form-select'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'العنوان'
            }),
            'role': forms.Select(attrs={
                'class': 'form-select'
            }),
            'department': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'القسم'
            }),
            'position': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'المنصب'
            }),
            'employee_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الموظف'
            }),
            'hire_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            })
        }
        labels = {
            'username': 'اسم المستخدم',
            'email': 'البريد الإلكتروني',
            'first_name': 'الاسم الأول',
            'last_name': 'الاسم الأخير',
            'phone': 'رقم الهاتف',
            'mobile': 'رقم الجوال',
            'national_id': 'رقم الهوية',
            'date_of_birth': 'تاريخ الميلاد',
            'gender': 'الجنس',
            'address': 'العنوان',
            'role': 'الدور',
            'department': 'القسم',
            'position': 'المنصب',
            'employee_id': 'رقم الموظف',
            'hire_date': 'تاريخ التوظيف'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make some fields optional
        optional_fields = [
            'phone', 'mobile', 'national_id', 'date_of_birth', 'gender',
            'address', 'department', 'position', 'employee_id', 'hire_date'
        ]
        for field in optional_fields:
            self.fields[field].required = False
        
        # Style password fields
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'كلمة المرور'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'تأكيد كلمة المرور'
        })

    def clean_email(self):
        email = self.cleaned_data['email']
        if User.objects.filter(email=email).exists():
            raise ValidationError('البريد الإلكتروني مستخدم مسبقاً')
        return email

    def clean_national_id(self):
        national_id = self.cleaned_data.get('national_id')
        if national_id and User.objects.filter(national_id=national_id).exists():
            raise ValidationError('رقم الهوية مستخدم مسبقاً')
        return national_id

    def clean_employee_id(self):
        employee_id = self.cleaned_data.get('employee_id')
        if employee_id and User.objects.filter(employee_id=employee_id).exists():
            raise ValidationError('رقم الموظف مستخدم مسبقاً')
        return employee_id


class CustomUserChangeForm(UserChangeForm):
    """Enhanced user edit form"""
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'first_name', 'last_name', 'phone', 'mobile',
            'national_id', 'date_of_birth', 'gender', 'address', 'role',
            'department', 'position', 'employee_id', 'hire_date', 'salary',
            'profile_picture', 'bio', 'website', 'language', 'timezone', 'theme',
            'email_notifications', 'sms_notifications', 'push_notifications',
            'is_active', 'is_staff', 'notes'
        ]
        widgets = {
            'username': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'phone': forms.TextInput(attrs={'class': 'form-control'}),
            'mobile': forms.TextInput(attrs={'class': 'form-control'}),
            'national_id': forms.TextInput(attrs={'class': 'form-control'}),
            'date_of_birth': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'gender': forms.Select(attrs={'class': 'form-select'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'role': forms.Select(attrs={'class': 'form-select'}),
            'department': forms.TextInput(attrs={'class': 'form-control'}),
            'position': forms.TextInput(attrs={'class': 'form-control'}),
            'employee_id': forms.TextInput(attrs={'class': 'form-control'}),
            'hire_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'salary': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'profile_picture': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
            'bio': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'website': forms.URLInput(attrs={'class': 'form-control'}),
            'language': forms.Select(attrs={'class': 'form-select'}),
            'timezone': forms.Select(attrs={'class': 'form-select'}),
            'theme': forms.Select(attrs={'class': 'form-select'}),
            'email_notifications': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'sms_notifications': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'push_notifications': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_staff': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3})
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Remove password field from change form
        if 'password' in self.fields:
            del self.fields['password']
        
        # Make some fields optional
        optional_fields = [
            'phone', 'mobile', 'national_id', 'date_of_birth', 'gender',
            'address', 'department', 'position', 'employee_id', 'hire_date',
            'salary', 'profile_picture', 'bio', 'website', 'notes'
        ]
        for field in optional_fields:
            if field in self.fields:
                self.fields[field].required = False


class UserRoleForm(forms.ModelForm):
    """Form for creating and editing user roles"""
    
    class Meta:
        model = UserRole
        fields = ['name', 'display_name', 'description', 'permissions', 'is_active']
        widgets = {
            'name': forms.Select(attrs={'class': 'form-select'}),
            'display_name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'permissions': forms.CheckboxSelectMultiple(),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }
        labels = {
            'name': 'نوع الدور',
            'display_name': 'الاسم المعروض',
            'description': 'الوصف',
            'permissions': 'الصلاحيات',
            'is_active': 'نشط'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['description'].required = False


class UserSearchForm(forms.Form):
    """Form for searching users"""
    
    search_query = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'ابحث في الاسم، البريد الإلكتروني، أو رقم الموظف...'
        }),
        label='البحث'
    )
    
    role = forms.ModelChoiceField(
        required=False,
        queryset=UserRole.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='الدور'
    )
    
    department = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        label='القسم'
    )
    
    is_active = forms.ChoiceField(
        required=False,
        choices=[('', 'الكل'), ('true', 'نشط'), ('false', 'غير نشط')],
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='الحالة'
    )
    
    is_staff = forms.ChoiceField(
        required=False,
        choices=[('', 'الكل'), ('true', 'موظف'), ('false', 'غير موظف')],
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='نوع المستخدم'
    )
    
    date_joined_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label='عضو منذ'
    )
    
    date_joined_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label='عضو حتى'
    )


class BulkUserActionForm(forms.Form):
    """Form for bulk actions on users"""
    
    ACTION_CHOICES = [
        ('activate', 'تفعيل'),
        ('deactivate', 'إلغاء التفعيل'),
        ('delete', 'حذف'),
        ('change_role', 'تغيير الدور'),
        ('send_notification', 'إرسال إشعار'),
    ]
    
    action = forms.ChoiceField(
        choices=ACTION_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='الإجراء'
    )
    
    role = forms.ModelChoiceField(
        required=False,
        queryset=UserRole.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='الدور الجديد'
    )
    
    notification_title = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        label='عنوان الإشعار'
    )
    
    notification_message = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        label='رسالة الإشعار'
    )
    
    selected_users = forms.CharField(
        widget=forms.HiddenInput(),
        label='المستخدمون المحددون'
    )

    def clean(self):
        cleaned_data = super().clean()
        action = cleaned_data.get('action')
        role = cleaned_data.get('role')
        notification_title = cleaned_data.get('notification_title')
        notification_message = cleaned_data.get('notification_message')
        
        if action == 'change_role' and not role:
            raise ValidationError('يجب اختيار دور عند تغيير الدور')
        
        if action == 'send_notification':
            if not notification_title or not notification_message:
                raise ValidationError('يجب إدخال عنوان ورسالة الإشعار')
        
        return cleaned_data


class UserProfileForm(forms.ModelForm):
    """Form for users to edit their own profile"""
    
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'email', 'phone', 'mobile',
            'address', 'profile_picture', 'bio', 'website',
            'language', 'timezone', 'theme',
            'email_notifications', 'sms_notifications', 'push_notifications'
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'phone': forms.TextInput(attrs={'class': 'form-control'}),
            'mobile': forms.TextInput(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'profile_picture': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
            'bio': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'website': forms.URLInput(attrs={'class': 'form-control'}),
            'language': forms.Select(attrs={'class': 'form-select'}),
            'timezone': forms.Select(attrs={'class': 'form-select'}),
            'theme': forms.Select(attrs={'class': 'form-select'}),
            'email_notifications': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'sms_notifications': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'push_notifications': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make some fields optional
        optional_fields = ['phone', 'mobile', 'address', 'profile_picture', 'bio', 'website']
        for field in optional_fields:
            self.fields[field].required = False
