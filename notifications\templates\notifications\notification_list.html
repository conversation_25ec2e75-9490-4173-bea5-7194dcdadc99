{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <h3 class="mb-4 fw-bold"><i class="bi bi-bell text-primary me-2"></i>كل الإشعارات</h3>
    <div class="row justify-content-center">
        <div class="col-lg-8">
            {% if notifications %}
                <div class="list-group mb-4">
                    {% for notif in notifications %}
                    <a href="{% url 'notifications:mark_as_read_and_redirect' notif.pk %}" class="list-group-item list-group-item-action notif-{{ 'unread' if not notif.is_read else 'read' }} d-flex justify-content-between align-items-start mb-2 rounded shadow-sm notification-hover">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">{{ notif.title }}</div>
                            <div class="small">{{ notif.message|truncatechars:100 }}</div>
                            <div class="text-muted small">{{ notif.created_at|date:"Y-m-d H:i" }}</div>
                        </div>
                        {% if not notif.is_read %}
                            <span class="badge bg-danger rounded-pill align-self-center">جديد</span>
                        {% endif %}
                    </a>
                    {% endfor %}
                </div>
                {% if is_paginated %}
                    <nav><ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابق</a></li>
                        {% endif %}
                        <li class="page-item disabled"><span class="page-link">صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span></li>
                        {% if page_obj.has_next %}
                            <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">التالي</a></li>
                        {% endif %}
                    </ul></nav>
                {% endif %}
            {% else %}
                <div class="alert alert-info text-center">لا توجد إشعارات بعد.</div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
<style>
.notification-hover {
    transition: box-shadow 0.15s, background 0.15s;
}
.notification-hover:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
    background: linear-gradient(90deg, #fffbe6 0%, #f8fafc 100%);
}
.notif-unread { background: #f8d7da; }
.notif-read { background: #f8f9fa; }
[data-theme='dark'] .notif-unread { background: #3a2323; }
[data-theme='dark'] .notif-read { background: #23272b; }
</style>
{% endblock %} 