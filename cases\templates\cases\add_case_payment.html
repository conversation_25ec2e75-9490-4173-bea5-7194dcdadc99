{% extends 'base.html' %}
{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg animate__animated animate__fadeIn rounded-3 overflow-hidden">
                <div class="card-header bg-primary text-white py-3">
                    <h4 class="mb-0 fw-bold">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة دفعة جديدة للقضية: {{ case.title }}
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        {% for field in form %}
                        <div class="mb-3 animate__animated animate__fadeInUp" style="animation-delay: {{ forloop.counter|add:'0.1' }}s">
                            <label for="{{ field.id_for_label }}" class="form-label fw-bold">
                                {{ field.label }}
                                {% if field.field.required %}<span class="text-danger">*</span>{% endif %}
                            </label>
                            {{ field }}
                            {% if field.help_text %}
                                <small class="text-muted">{{ field.help_text }}</small>
                            {% endif %}
                            {% for error in field.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endfor %}
                        <div class="d-flex justify-content-between mt-4">
                            <button type="submit" class="btn btn-primary px-4 py-2 rounded-pill shadow-sm" id="savePaymentBtn">
                                <i class="bi bi-check-circle me-2"></i> حفظ الدفعة
                            </button>
                            <a href="{% url 'cases:case_accounts' case.id %}" class="btn btn-outline-secondary px-4 py-2 rounded-pill">
                                <i class="bi bi-arrow-left-circle me-2"></i> العودة للحسابات
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
<style>
.card {
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

#savePaymentBtn {
    transition: all 0.3s;
}

#savePaymentBtn:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3) !important;
}

.form-control {
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.invalid-feedback {
    animation: fadeIn 0.3s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>
{% endblock %}
