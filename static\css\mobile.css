/* Mobile-First Responsive Design for Lawyers Office System */

/* Mobile Base Styles (320px and up) */
@media screen and (max-width: 767px) {
    
    /* Layout Adjustments */
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
    
    .content-wrapper {
        padding: 1rem 0.5rem;
    }
    
    /* Typography */
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
    h4 { font-size: 1.1rem; }
    h5 { font-size: 1rem; }
    
    /* Sidebar Mobile Optimization */
    .sidebar {
        position: fixed;
        top: 0;
        right: -100%;
        width: 280px;
        height: 100vh;
        z-index: 1050;
        transition: right 0.3s ease;
        box-shadow: -5px 0 15px rgba(0,0,0,0.1);
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 1040;
        display: none;
    }
    
    .sidebar.show + .sidebar-overlay {
        display: block;
    }
    
    .main-content {
        margin-right: 0;
        width: 100%;
    }
    
    /* Navigation Enhancements */
    .navbar {
        padding: 0.5rem 1rem;
    }
    
    .navbar .container-fluid {
        flex-wrap: nowrap;
    }
    
    .mobile-menu-toggle {
        display: block !important;
        order: -1;
        margin-left: 0.5rem;
    }
    
    /* Search Bar Mobile */
    .navbar .input-group {
        max-width: none;
        width: 100%;
        margin: 0.5rem 0;
    }
    
    .d-none.d-md-flex {
        display: none !important;
    }
    
    /* Cards and Components */
    .card {
        margin-bottom: 1rem;
        border-radius: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .dashboard-link-card {
        margin-bottom: 1rem;
        min-height: 120px;
    }
    
    .dashboard-link-card .card-body {
        padding: 1.5rem 1rem;
        text-align: center;
    }
    
    /* Tables Mobile Optimization */
    .table-responsive {
        border: none;
        margin-bottom: 1rem;
    }
    
    .table {
        font-size: 0.875rem;
        margin-bottom: 0;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        vertical-align: middle;
    }
    
    /* Hide less important columns on mobile */
    .table .d-none.d-md-table-cell {
        display: none !important;
    }
    
    /* Mobile Table Cards */
    .mobile-table-card {
        display: block;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        margin-bottom: 0.75rem;
        padding: 1rem;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    .mobile-table-card .card-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--primary-color);
    }
    
    .mobile-table-card .card-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }
    
    .mobile-table-card .card-actions {
        margin-top: 0.75rem;
        padding-top: 0.75rem;
        border-top: 1px solid #eee;
    }
    
    /* Forms Mobile Optimization */
    .form-control,
    .form-select {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 0.75rem;
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .form-group {
        margin-bottom: 1.25rem;
    }
    
    /* Button Adjustments */
    .btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.9rem;
        border-radius: 0.5rem;
        min-height: 44px; /* Touch target size */
    }
    
    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        min-height: 38px;
    }
    
    .btn-group-vertical .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    /* Modal Mobile Optimization */
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .modal-content {
        border-radius: 0.75rem;
    }
    
    .modal-header {
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .modal-body {
        padding: 1rem;
        max-height: 60vh;
        overflow-y: auto;
    }
    
    .modal-footer {
        padding: 1rem;
        border-top: 1px solid #dee2e6;
    }
    
    /* Dropdown Mobile */
    .dropdown-menu {
        border-radius: 0.75rem;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border: none;
        min-width: 200px;
    }
    
    .dropdown-item {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    /* Breadcrumb Mobile */
    .breadcrumb {
        background: none;
        padding: 0.5rem 0;
        margin-bottom: 1rem;
        font-size: 0.875rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "‹";
        font-size: 1.2rem;
    }
    
    /* Alert Mobile */
    .alert {
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        font-size: 0.9rem;
    }
    
    /* Pagination Mobile */
    .pagination {
        justify-content: center;
        margin-top: 1.5rem;
    }
    
    .page-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        min-width: 44px;
        text-align: center;
    }
    
    /* Statistics Cards */
    .stats-card {
        text-align: center;
        padding: 1.5rem 1rem;
        margin-bottom: 1rem;
        border-radius: 0.75rem;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }
    
    .stats-card .stats-number {
        font-size: 2rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0.5rem;
    }
    
    .stats-card .stats-label {
        font-size: 0.875rem;
        color: #6c757d;
        font-weight: 500;
    }
    
    /* Calendar Mobile */
    .calendar-container {
        overflow-x: auto;
        margin: 0 -0.75rem;
        padding: 0 0.75rem;
    }
    
    .calendar-grid {
        min-width: 300px;
    }
    
    /* File Upload Mobile */
    .file-upload-area {
        padding: 2rem 1rem;
        border: 2px dashed #dee2e6;
        border-radius: 0.75rem;
        text-align: center;
        background: #f8f9fa;
        margin-bottom: 1rem;
    }
    
    .file-upload-area.dragover {
        border-color: var(--primary-color);
        background: rgba(13, 110, 253, 0.05);
    }
    
    /* Search Results Mobile */
    .search-result-item {
        padding: 1rem;
        border-bottom: 1px solid #eee;
        background: white;
        margin-bottom: 0.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    .search-result-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--primary-color);
    }
    
    .search-result-meta {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }
    
    /* Footer Mobile */
    footer {
        padding: 1.5rem 0;
        text-align: center;
        font-size: 0.875rem;
    }
    
    footer .row > div {
        margin-bottom: 1rem;
    }
    
    /* Utility Classes */
    .mobile-only {
        display: block !important;
    }
    
    .mobile-hidden {
        display: none !important;
    }
    
    .text-mobile-center {
        text-align: center !important;
    }
    
    .mb-mobile-3 {
        margin-bottom: 1rem !important;
    }
    
    .p-mobile-2 {
        padding: 0.5rem !important;
    }
    
    /* Touch Improvements */
    .btn,
    .nav-link,
    .dropdown-item,
    .page-link {
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
    }
    
    /* Scroll Improvements */
    .table-responsive {
        -webkit-overflow-scrolling: touch;
    }
    
    /* Loading States */
    .loading-mobile {
        text-align: center;
        padding: 2rem;
    }
    
    .loading-mobile .spinner-border {
        width: 2rem;
        height: 2rem;
    }
}

/* Tablet Styles (768px to 991px) */
@media screen and (min-width: 768px) and (max-width: 991px) {
    .sidebar {
        width: 250px;
    }
    
    .main-content {
        margin-right: 250px;
    }
    
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .content-wrapper {
        padding: 1.5rem 1rem;
    }
    
    .dashboard-link-card {
        min-height: 140px;
    }
    
    .table th,
    .table td {
        padding: 0.75rem 0.5rem;
    }
}

/* Large Mobile Landscape (576px to 767px) */
@media screen and (min-width: 576px) and (max-width: 767px) {
    .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto;
    }
    
    .dashboard-link-card {
        min-height: 130px;
    }
    
    .btn {
        padding: 0.75rem 1.5rem;
    }
    
    .form-control,
    .form-select {
        padding: 0.75rem 1rem;
    }
}

/* Small Mobile (up to 575px) */
@media screen and (max-width: 575px) {
    .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    
    .content-wrapper {
        padding: 0.75rem 0.25rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.625rem 1rem;
    }
    
    .form-control,
    .form-select {
        font-size: 16px;
        padding: 0.625rem 0.75rem;
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .table th,
    .table td {
        padding: 0.375rem 0.25rem;
    }
    
    .modal-dialog {
        margin: 0.25rem;
        max-width: calc(100% - 0.5rem);
    }
    
    .dashboard-link-card {
        min-height: 100px;
    }
    
    .dashboard-link-card .card-body {
        padding: 1rem 0.75rem;
    }
}
