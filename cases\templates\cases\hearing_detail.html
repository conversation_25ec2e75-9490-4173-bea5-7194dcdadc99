{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-12 col-md-10 col-lg-8">
            <div class="card shadow rounded-4 border-0">
                <div class="card-body p-4">
                    <h3 class="mb-4 text-center fw-bold">
                        <i class="bi bi-calendar-event text-success me-2"></i>
                        تفاصيل الجلسة
                    </h3>
                    <ul class="list-group list-group-flush mb-4">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-briefcase me-2 text-secondary"></i>القضية:</span>
                            <span class="fw-bold">{{ hearing.case }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-calendar2-week me-2 text-secondary"></i>تاريخ ووقت الجلسة:</span>
                            <span class="fw-bold">{{ hearing.date|date:"Y-m-d H:i" }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-geo-alt me-2 text-secondary"></i>المكان:</span>
                            <span class="fw-bold">{{ hearing.location|default:"-" }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-card-text me-2 text-secondary"></i>الوصف:</span>
                            <span class="fw-bold">{{ hearing.description|default:"-" }}</span>
                        </li>
                    </ul>
                    <hr>
                    <!-- مستندات الجلسة -->
                    <div class="d-flex justify-content-between align-items-center mt-4 mb-3">
                        <h5 class="fw-bold mb-0"><i class="bi bi-file-earmark-text me-2"></i>المستندات</h5>
                        <a href="{% url 'cases:hearingdocument_add' hearing.pk %}" class="btn btn-success btn-sm px-3">
                            <i class="bi bi-plus-circle"></i> إضافة مستند
                        </a>
                    </div>
                    {% if documents %}
                        <ul class="list-group mb-3">
                            {% for doc in documents %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>{{ doc.description|default:doc.file.name }}</span>
                                <span>
                                    <a href="{{ doc.file.url }}" target="_blank" class="btn btn-sm btn-outline-primary"><i class="bi bi-download"></i> تحميل</a>
                                    <a href="{% url 'cases:hearingdocument_edit' doc.pk %}" class="btn btn-sm btn-outline-secondary ms-1"><i class="bi bi-pencil"></i> تعديل</a>
                                    <a href="{% url 'cases:hearingdocument_delete' doc.pk %}" class="btn btn-sm btn-outline-danger ms-1"><i class="bi bi-trash"></i> حذف</a>
                                </span>
                            </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="alert alert-info text-center">لا توجد مستندات لهذه الجلسة.</div>
                    {% endif %}
                    <!-- قرار المحكمة -->
                    <div class="d-flex justify-content-between align-items-center mt-4 mb-3">
                        <h5 class="fw-bold mb-0"><i class="bi bi-gavel me-2"></i>قرار المحكمة</h5>
                        <a href="{% url 'cases:courtdecision_add_or_update' hearing.pk %}" class="btn btn-success btn-sm px-3">
                            <i class="bi bi-plus-circle"></i>
                            {% if court_decision %}تعديل القرار{% else %}إضافة قرار{% endif %}
                        </a>
                        {% if court_decision %}
                        <a href="{% url 'cases:courtdecision_delete' court_decision.pk %}" class="btn btn-danger btn-sm px-3 ms-2">
                            <i class="bi bi-trash"></i> حذف القرار
                        </a>
                        {% endif %}
                    </div>
                    {% if court_decision %}
                        <div class="mb-3">
                            <div class="mb-2"><strong>النص:</strong> {{ court_decision.text|linebreaks }}</div>
                            {% if court_decision.file %}
                                <a href="{{ court_decision.file.url }}" target="_blank" class="btn btn-sm btn-outline-secondary"><i class="bi bi-download"></i> تحميل القرار</a>
                            {% endif %}
                            {% if court_decision.date_issued %}
                                <div class="mt-2"><strong>تاريخ الإصدار:</strong> {{ court_decision.date_issued }}</div>
                            {% endif %}
                        </div>
                    {% else %}
                        <div class="alert alert-info text-center">لا يوجد قرار محكمة لهذه الجلسة.</div>
                    {% endif %}
                    <!-- الإجراءات -->
                    <div class="d-flex justify-content-between align-items-center mt-4 mb-3">
                        <h5 class="fw-bold mb-0"><i class="bi bi-list-check me-2"></i>الإجراءات</h5>
                        <a href="{% url 'cases:hearingaction_add' hearing.pk %}" class="btn btn-success btn-sm px-3">
                            <i class="bi bi-plus-circle"></i> إضافة إجراء
                        </a>
                    </div>
                    {% if actions %}
                        <ul class="list-group mb-3">
                            {% for action in actions %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>{{ action.action }}</span>
                                <span>
                                    <span class="badge bg-{% if action.is_completed %}success{% else %}warning{% endif %}">{% if action.is_completed %}تم{% else %}قيد التنفيذ{% endif %}</span>
                                    {% if action.due_date %}<span class="ms-2 text-muted small">({{ action.due_date }})</span>{% endif %}
                                    <a href="{% url 'cases:hearingaction_edit' action.pk %}" class="btn btn-sm btn-outline-secondary ms-1"><i class="bi bi-pencil"></i> تعديل</a>
                                    <a href="{% url 'cases:hearingaction_delete' action.pk %}" class="btn btn-sm btn-outline-danger ms-1"><i class="bi bi-trash"></i> حذف</a>
                                </span>
                            </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="alert alert-info text-center">لا توجد إجراءات لهذه الجلسة.</div>
                    {% endif %}
                    <!-- التواريخ المهمة -->
                    <div class="d-flex justify-content-between align-items-center mt-4 mb-3">
                        <h5 class="fw-bold mb-0"><i class="bi bi-calendar-event me-2"></i>تواريخ مهمة</h5>
                        <a href="{% url 'cases:hearingimportantdate_add' hearing.pk %}" class="btn btn-success btn-sm px-3">
                            <i class="bi bi-plus-circle"></i> إضافة تاريخ مهم
                        </a>
                    </div>
                    {% if important_dates %}
                        <ul class="list-group mb-3">
                            {% for date in important_dates %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>{{ date.date }}</span>
                                <span>
                                    {{ date.description|default:"-" }}
                                    <a href="{% url 'cases:hearingimportantdate_edit' date.pk %}" class="btn btn-sm btn-outline-secondary ms-1"><i class="bi bi-pencil"></i> تعديل</a>
                                    <a href="{% url 'cases:hearingimportantdate_delete' date.pk %}" class="btn btn-sm btn-outline-danger ms-1"><i class="bi bi-trash"></i> حذف</a>
                                </span>
                            </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="alert alert-info text-center">لا توجد تواريخ مهمة لهذه الجلسة.</div>
                    {% endif %}
                    <!-- التذكيرات -->
                    <div class="d-flex justify-content-between align-items-center mt-4 mb-3">
                        <h5 class="fw-bold mb-0"><i class="bi bi-bell me-2"></i>التذكيرات</h5>
                        <a href="{% url 'cases:hearingreminder_add' hearing.pk %}" class="btn btn-success btn-sm px-3">
                            <i class="bi bi-plus-circle"></i> إضافة تذكير
                        </a>
                    </div>
                    {% if reminders %}
                        <ul class="list-group mb-3">
                            {% for reminder in reminders %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>{{ reminder.reminder_date|date:"Y-m-d H:i" }}</span>
                                <span>
                                    {{ reminder.note|default:"-" }}
                                    <span class="badge bg-{% if reminder.is_sent %}success{% else %}secondary{% endif %} ms-2">{% if reminder.is_sent %}تم الإرسال{% else %}لم يُرسل{% endif %}</span>
                                    <a href="{% url 'cases:hearingreminder_edit' reminder.pk %}" class="btn btn-sm btn-outline-secondary ms-1"><i class="bi bi-pencil"></i> تعديل</a>
                                    <a href="{% url 'cases:hearingreminder_delete' reminder.pk %}" class="btn btn-sm btn-outline-danger ms-1"><i class="bi bi-trash"></i> حذف</a>
                                </span>
                            </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="alert alert-info text-center">لا توجد تذكيرات لهذه الجلسة.</div>
                    {% endif %}
                    <div class="mt-4 text-end">
                        <a href="{% url 'cases:detail' hearing.case.pk %}" class="btn btn-link">
                            <i class="bi bi-arrow-right-circle"></i> العودة لتفاصيل القضية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
{% endblock %} 