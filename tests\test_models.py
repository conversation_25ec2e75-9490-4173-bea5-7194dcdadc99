"""
Comprehensive test suite for Lawyers Office System models
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime, timedelta

from cases.models import Case, Hearing, Fee
from clients.models import Client, Payment
from appointments.models import Appointment
from documents.models import Document, DocumentCategory
from notifications.models import Notification
from users.models import UserRole

User = get_user_model()

class UserModelTest(TestCase):
    """Test User model functionality"""
    
    def setUp(self):
        self.role = UserRole.objects.create(
            name='lawyer',
            display_name='محامي',
            description='محامي في المكتب'
        )
    
    def test_user_creation(self):
        """Test user creation with all fields"""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='أحمد',
            last_name='محمد',
            phone='**********',
            national_id='**********',
            role=self.role
        )
        
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.full_name, 'أحمد محمد')
        self.assertEqual(user.role, self.role)
        self.assertTrue(user.check_password('testpass123'))
    
    def test_user_age_calculation(self):
        """Test age calculation"""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            date_of_birth=datetime(1990, 1, 1).date()
        )
        
        expected_age = timezone.now().year - 1990
        self.assertEqual(user.age, expected_age)
    
    def test_account_locking(self):
        """Test account locking functionality"""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Test locking
        user.lock_account(30)
        self.assertTrue(user.is_account_locked)
        
        # Test unlocking
        user.unlock_account()
        self.assertFalse(user.is_account_locked)
        self.assertEqual(user.failed_login_attempts, 0)

class ClientModelTest(TestCase):
    """Test Client model functionality"""
    
    def test_client_creation(self):
        """Test client creation"""
        client = Client.objects.create(
            full_name='محمد أحمد',
            national_id='**********',
            phone='**********',
            email='<EMAIL>'
        )
        
        self.assertEqual(client.full_name, 'محمد أحمد')
        self.assertEqual(client.national_id, '**********')
        self.assertEqual(str(client), 'محمد أحمد')
    
    def test_client_unique_constraints(self):
        """Test unique constraints"""
        Client.objects.create(
            full_name='محمد أحمد',
            national_id='**********',
            phone='**********'
        )
        
        # Should raise error for duplicate national_id
        with self.assertRaises(Exception):
            Client.objects.create(
                full_name='أحمد محمد',
                national_id='**********',
                phone='0501234568'
            )

class CaseModelTest(TestCase):
    """Test Case model functionality"""
    
    def setUp(self):
        self.client = Client.objects.create(
            full_name='محمد أحمد',
            national_id='**********',
            phone='**********'
        )
        
        self.lawyer = User.objects.create_user(
            username='lawyer1',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_case_creation(self):
        """Test case creation"""
        case = Case.objects.create(
            case_number='2024/001',
            title='قضية تجارية',
            description='وصف القضية',
            client=self.client,
            lawyer=self.lawyer,
            start_date=timezone.now().date(),
            status='open'
        )
        
        self.assertEqual(case.case_number, '2024/001')
        self.assertEqual(case.title, 'قضية تجارية')
        self.assertEqual(case.client, self.client)
        self.assertEqual(case.lawyer, self.lawyer)
        self.assertEqual(case.status, 'open')
    
    def test_case_unique_number(self):
        """Test case number uniqueness"""
        Case.objects.create(
            case_number='2024/001',
            title='قضية أولى',
            client=self.client,
            lawyer=self.lawyer,
            start_date=timezone.now().date()
        )
        
        # Should raise error for duplicate case number
        with self.assertRaises(Exception):
            Case.objects.create(
                case_number='2024/001',
                title='قضية ثانية',
                client=self.client,
                lawyer=self.lawyer,
                start_date=timezone.now().date()
            )

class AppointmentModelTest(TestCase):
    """Test Appointment model functionality"""
    
    def setUp(self):
        self.client = Client.objects.create(
            full_name='محمد أحمد',
            national_id='**********',
            phone='**********'
        )
        
        self.lawyer = User.objects.create_user(
            username='lawyer1',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_appointment_creation(self):
        """Test appointment creation"""
        appointment_time = timezone.now() + timedelta(days=1)
        
        appointment = Appointment.objects.create(
            title='استشارة قانونية',
            client=self.client,
            lawyer=self.lawyer,
            datetime=appointment_time,
            location='المكتب الرئيسي'
        )
        
        self.assertEqual(appointment.title, 'استشارة قانونية')
        self.assertEqual(appointment.client, self.client)
        self.assertEqual(appointment.lawyer, self.lawyer)
        self.assertEqual(appointment.location, 'المكتب الرئيسي')
    
    def test_appointment_past_validation(self):
        """Test validation for past appointments"""
        past_time = timezone.now() - timedelta(days=1)
        
        appointment = Appointment(
            title='استشارة قانونية',
            client=self.client,
            lawyer=self.lawyer,
            datetime=past_time,
            location='المكتب الرئيسي'
        )
        
        # This should be handled in forms/views, not model level
        # But we can test the datetime field accepts past dates
        appointment.save()
        self.assertTrue(appointment.id)

class DocumentModelTest(TestCase):
    """Test Document model functionality"""
    
    def setUp(self):
        self.client = Client.objects.create(
            full_name='محمد أحمد',
            national_id='**********',
            phone='**********'
        )
        
        self.category = DocumentCategory.objects.create(
            name='عقود',
            description='فئة العقود'
        )
        
        self.user = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_document_creation(self):
        """Test document creation"""
        document = Document.objects.create(
            name='عقد إيجار',
            description='عقد إيجار شقة',
            client=self.client,
            category=self.category,
            uploaded_by=self.user,
            document_type='contract'
        )
        
        self.assertEqual(document.name, 'عقد إيجار')
        self.assertEqual(document.client, self.client)
        self.assertEqual(document.category, self.category)
        self.assertEqual(document.uploaded_by, self.user)
        self.assertEqual(document.document_type, 'contract')
    
    def test_document_tags(self):
        """Test document tags functionality"""
        document = Document.objects.create(
            name='مستند تجريبي',
            client=self.client,
            uploaded_by=self.user,
            tags='عقد, إيجار, سكني'
        )
        
        tags_list = document.get_tags_list()
        self.assertEqual(len(tags_list), 3)
        self.assertIn('عقد', tags_list)
        self.assertIn('إيجار', tags_list)
        self.assertIn('سكني', tags_list)

class NotificationModelTest(TestCase):
    """Test Notification model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_notification_creation(self):
        """Test notification creation"""
        notification = Notification.objects.create(
            user=self.user,
            title='إشعار تجريبي',
            message='هذا إشعار تجريبي',
            notification_type='general'
        )
        
        self.assertEqual(notification.user, self.user)
        self.assertEqual(notification.title, 'إشعار تجريبي')
        self.assertEqual(notification.message, 'هذا إشعار تجريبي')
        self.assertFalse(notification.is_read)
    
    def test_notification_mark_as_read(self):
        """Test marking notification as read"""
        notification = Notification.objects.create(
            user=self.user,
            title='إشعار تجريبي',
            message='هذا إشعار تجريبي'
        )
        
        notification.mark_as_read()
        self.assertTrue(notification.is_read)
        self.assertIsNotNone(notification.read_at)

class PaymentModelTest(TestCase):
    """Test Payment model functionality"""
    
    def setUp(self):
        self.client = Client.objects.create(
            full_name='محمد أحمد',
            national_id='**********',
            phone='**********'
        )
        
        self.lawyer = User.objects.create_user(
            username='lawyer1',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.case = Case.objects.create(
            case_number='2024/001',
            title='قضية تجارية',
            client=self.client,
            lawyer=self.lawyer,
            start_date=timezone.now().date()
        )
    
    def test_payment_creation(self):
        """Test payment creation"""
        payment = Payment.objects.create(
            case=self.case,
            amount=1000.00,
            date=timezone.now().date(),
            notes='دفعة أولى'
        )
        
        self.assertEqual(payment.case, self.case)
        self.assertEqual(payment.amount, 1000.00)
        self.assertEqual(payment.notes, 'دفعة أولى')

class FeeModelTest(TestCase):
    """Test Fee model functionality"""
    
    def setUp(self):
        self.client = Client.objects.create(
            full_name='محمد أحمد',
            national_id='**********',
            phone='**********'
        )
        
        self.lawyer = User.objects.create_user(
            username='lawyer1',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.case = Case.objects.create(
            case_number='2024/001',
            title='قضية تجارية',
            client=self.client,
            lawyer=self.lawyer,
            start_date=timezone.now().date()
        )
    
    def test_fee_creation(self):
        """Test fee creation"""
        fee = Fee.objects.create(
            case=self.case,
            fee_type='consultation',
            amount=500.00,
            due_date=timezone.now().date() + timedelta(days=30),
            status='pending'
        )
        
        self.assertEqual(fee.case, self.case)
        self.assertEqual(fee.fee_type, 'consultation')
        self.assertEqual(fee.amount, 500.00)
        self.assertEqual(fee.status, 'pending')
    
    def test_fee_status_change(self):
        """Test fee status change"""
        fee = Fee.objects.create(
            case=self.case,
            fee_type='consultation',
            amount=500.00,
            due_date=timezone.now().date() + timedelta(days=30),
            status='pending'
        )
        
        fee.status = 'paid'
        fee.paid_date = timezone.now().date()
        fee.save()
        
        self.assertEqual(fee.status, 'paid')
        self.assertIsNotNone(fee.paid_date)
