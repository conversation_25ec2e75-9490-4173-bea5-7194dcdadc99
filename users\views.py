from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth import get_user_model, update_session_auth_hash
from django.contrib import messages
from django.http import JsonResponse, HttpResponseForbidden
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.contrib.auth.forms import PasswordChangeForm
from .models import UserRole
from .forms import (
    CustomUserCreationForm, CustomUserChangeForm, UserRoleForm,
    UserSearchForm, BulkUserActionForm, UserProfileForm
)
from notifications.utils import create_notification
import json

User = get_user_model()

def is_admin_or_staff(user):
    """Check if user is admin or staff"""
    return user.is_authenticated and (user.is_superuser or user.is_staff)

@login_required
@user_passes_test(is_admin_or_staff)
def user_list(request):
    """List all users with search and filtering"""
    form = UserSearchForm(request.GET)
    users = User.objects.select_related('role').all()

    if form.is_valid():
        search_query = form.cleaned_data.get('search_query')
        role = form.cleaned_data.get('role')
        department = form.cleaned_data.get('department')
        is_active = form.cleaned_data.get('is_active')
        is_staff = form.cleaned_data.get('is_staff')
        date_joined_from = form.cleaned_data.get('date_joined_from')
        date_joined_to = form.cleaned_data.get('date_joined_to')

        if search_query:
            users = users.filter(
                Q(username__icontains=search_query) |
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(employee_id__icontains=search_query)
            )

        if role:
            users = users.filter(role=role)

        if department:
            users = users.filter(department__icontains=department)

        if is_active == 'true':
            users = users.filter(is_active=True)
        elif is_active == 'false':
            users = users.filter(is_active=False)

        if is_staff == 'true':
            users = users.filter(is_staff=True)
        elif is_staff == 'false':
            users = users.filter(is_staff=False)

        if date_joined_from:
            users = users.filter(date_joined__date__gte=date_joined_from)

        if date_joined_to:
            users = users.filter(date_joined__date__lte=date_joined_to)

    # Pagination
    paginator = Paginator(users, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    stats = {
        'total_users': User.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
        'staff_users': User.objects.filter(is_staff=True).count(),
        'recent_users': User.objects.filter(
            date_joined__gte=timezone.now() - timezone.timedelta(days=30)
        ).count(),
    }

    context = {
        'page_obj': page_obj,
        'form': form,
        'stats': stats,
        'bulk_form': BulkUserActionForm(),
    }

    return render(request, 'users/user_list.html', context)

@login_required
@user_passes_test(is_admin_or_staff)
def user_detail(request, user_id):
    """View user details"""
    user = get_object_or_404(User, id=user_id)

    # Get user statistics
    user_stats = user.get_dashboard_stats()

    # Recent activity (you can implement this based on your activity logging)
    recent_activity = []

    context = {
        'user_obj': user,  # Using user_obj to avoid conflict with request.user
        'user_stats': user_stats,
        'recent_activity': recent_activity,
    }

    return render(request, 'users/user_detail.html', context)

@login_required
@user_passes_test(is_admin_or_staff)
def user_create(request):
    """Create new user"""
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST, request.FILES)
        if form.is_valid():
            user = form.save()

            # Create notification for new user
            create_notification(
                user=user,
                title='مرحباً بك في نظام مكتب المحاماة',
                message='تم إنشاء حسابك بنجاح. يمكنك الآن تسجيل الدخول والبدء في استخدام النظام.',
                notification_type='welcome',
                send_email=True
            )

            messages.success(request, f'تم إنشاء المستخدم {user.username} بنجاح')
            return redirect('users:user_detail', user_id=user.id)
    else:
        form = CustomUserCreationForm()

    return render(request, 'users/user_form.html', {
        'form': form,
        'title': 'إنشاء مستخدم جديد'
    })

@login_required
@user_passes_test(is_admin_or_staff)
def user_edit(request, user_id):
    """Edit user"""
    user = get_object_or_404(User, id=user_id)

    if request.method == 'POST':
        form = CustomUserChangeForm(request.POST, request.FILES, instance=user)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات المستخدم {user.username} بنجاح')
            return redirect('users:user_detail', user_id=user.id)
    else:
        form = CustomUserChangeForm(instance=user)

    return render(request, 'users/user_form.html', {
        'form': form,
        'user_obj': user,
        'title': f'تعديل المستخدم: {user.username}'
    })

@login_required
@user_passes_test(is_admin_or_staff)
@require_http_methods(["POST"])
def user_delete(request, user_id):
    """Delete user"""
    user = get_object_or_404(User, id=user_id)

    if user == request.user:
        return JsonResponse({'success': False, 'message': 'لا يمكنك حذف حسابك الخاص'})

    if user.is_superuser and not request.user.is_superuser:
        return JsonResponse({'success': False, 'message': 'لا يمكنك حذف مدير عام'})

    username = user.username
    user.delete()

    return JsonResponse({
        'success': True,
        'message': f'تم حذف المستخدم {username} بنجاح'
    })

@login_required
def user_profile(request):
    """User profile page"""
    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث ملفك الشخصي بنجاح')
            return redirect('users:profile')
    else:
        form = UserProfileForm(instance=request.user)

    # Get user statistics
    user_stats = request.user.get_dashboard_stats()

    context = {
        'form': form,
        'user_stats': user_stats,
    }

    return render(request, 'users/profile.html', context)

@login_required
def change_password(request):
    """Change user password"""
    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            user.last_password_change = timezone.now()
            user.save(update_fields=['last_password_change'])

            update_session_auth_hash(request, user)  # Important!
            messages.success(request, 'تم تغيير كلمة المرور بنجاح')
            return redirect('users:profile')
    else:
        form = PasswordChangeForm(request.user)

    return render(request, 'users/change_password.html', {'form': form})

# Role Management Views

@login_required
@user_passes_test(is_admin_or_staff)
def role_list(request):
    """List all user roles"""
    roles = UserRole.objects.annotate(user_count=Count('user')).all()

    context = {
        'roles': roles,
    }

    return render(request, 'users/role_list.html', context)

@login_required
@user_passes_test(is_admin_or_staff)
def role_detail(request, role_id):
    """View role details"""
    role = get_object_or_404(UserRole, id=role_id)
    users_with_role = User.objects.filter(role=role)

    context = {
        'role': role,
        'users_with_role': users_with_role,
    }

    return render(request, 'users/role_detail.html', context)

@login_required
@user_passes_test(is_admin_or_staff)
def role_create(request):
    """Create new role"""
    if request.method == 'POST':
        form = UserRoleForm(request.POST)
        if form.is_valid():
            role = form.save()
            messages.success(request, f'تم إنشاء الدور {role.display_name} بنجاح')
            return redirect('users:role_detail', role_id=role.id)
    else:
        form = UserRoleForm()

    return render(request, 'users/role_form.html', {
        'form': form,
        'title': 'إنشاء دور جديد'
    })

@login_required
@user_passes_test(is_admin_or_staff)
def role_edit(request, role_id):
    """Edit role"""
    role = get_object_or_404(UserRole, id=role_id)

    if request.method == 'POST':
        form = UserRoleForm(request.POST, instance=role)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث الدور {role.display_name} بنجاح')
            return redirect('users:role_detail', role_id=role.id)
    else:
        form = UserRoleForm(instance=role)

    return render(request, 'users/role_form.html', {
        'form': form,
        'role': role,
        'title': f'تعديل الدور: {role.display_name}'
    })

@login_required
@user_passes_test(is_admin_or_staff)
@require_http_methods(["POST"])
def role_delete(request, role_id):
    """Delete role"""
    role = get_object_or_404(UserRole, id=role_id)

    # Check if role is in use
    if role.user_set.exists():
        return JsonResponse({
            'success': False,
            'message': 'لا يمكن حذف الدور لأنه مستخدم من قبل مستخدمين'
        })

    role_name = role.display_name
    role.delete()

    return JsonResponse({
        'success': True,
        'message': f'تم حذف الدور {role_name} بنجاح'
    })

# Bulk Actions

@login_required
@user_passes_test(is_admin_or_staff)
@require_http_methods(["POST"])
def bulk_user_action(request):
    """Handle bulk actions on users"""
    form = BulkUserActionForm(request.POST)

    if not form.is_valid():
        return JsonResponse({'success': False, 'message': 'بيانات غير صحيحة'})

    action = form.cleaned_data['action']
    selected_users = form.cleaned_data['selected_users']

    try:
        user_ids = [int(id) for id in selected_users.split(',') if id]
        users = User.objects.filter(id__in=user_ids)

        if action == 'activate':
            users.update(is_active=True)
            message = f'تم تفعيل {users.count()} مستخدم'

        elif action == 'deactivate':
            # Don't deactivate superusers or current user
            users = users.exclude(is_superuser=True).exclude(id=request.user.id)
            users.update(is_active=False)
            message = f'تم إلغاء تفعيل {users.count()} مستخدم'

        elif action == 'delete':
            # Don't delete superusers or current user
            users = users.exclude(is_superuser=True).exclude(id=request.user.id)
            count = users.count()
            users.delete()
            message = f'تم حذف {count} مستخدم'

        elif action == 'change_role':
            role = form.cleaned_data['role']
            users.update(role=role)
            message = f'تم تغيير دور {users.count()} مستخدم إلى {role.display_name}'

        elif action == 'send_notification':
            title = form.cleaned_data['notification_title']
            message_text = form.cleaned_data['notification_message']

            for user in users:
                create_notification(
                    user=user,
                    title=title,
                    message=message_text,
                    notification_type='admin',
                    send_email=True
                )

            message = f'تم إرسال إشعار إلى {users.count()} مستخدم'

        return JsonResponse({'success': True, 'message': message})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# API Views

@login_required
@user_passes_test(is_admin_or_staff)
def user_search_api(request):
    """API endpoint for user search"""
    query = request.GET.get('q', '')

    if len(query) < 2:
        return JsonResponse({'users': []})

    users = User.objects.filter(
        Q(username__icontains=query) |
        Q(first_name__icontains=query) |
        Q(last_name__icontains=query) |
        Q(email__icontains=query)
    )[:10]

    user_data = []
    for user in users:
        user_data.append({
            'id': user.id,
            'username': user.username,
            'full_name': user.get_full_name(),
            'email': user.email,
            'role': user.role.display_name if user.role else '',
            'is_active': user.is_active,
        })

    return JsonResponse({'users': user_data})

@login_required
@user_passes_test(is_admin_or_staff)
def user_stats_api(request):
    """API endpoint for user statistics"""
    stats = {
        'total_users': User.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
        'inactive_users': User.objects.filter(is_active=False).count(),
        'staff_users': User.objects.filter(is_staff=True).count(),
        'recent_users': User.objects.filter(
            date_joined__gte=timezone.now() - timezone.timedelta(days=30)
        ).count(),
    }

    # Role distribution
    role_stats = []
    for role in UserRole.objects.annotate(user_count=Count('user')):
        role_stats.append({
            'name': role.display_name,
            'count': role.user_count
        })

    stats['role_distribution'] = role_stats

    return JsonResponse(stats)

@login_required
@user_passes_test(is_admin_or_staff)
@require_http_methods(["POST"])
def toggle_user_active(request, user_id):
    """Toggle user active status"""
    user = get_object_or_404(User, id=user_id)

    if user == request.user:
        return JsonResponse({'success': False, 'message': 'لا يمكنك تغيير حالة حسابك الخاص'})

    if user.is_superuser and not request.user.is_superuser:
        return JsonResponse({'success': False, 'message': 'لا يمكنك تغيير حالة مدير عام'})

    user.is_active = not user.is_active
    user.save(update_fields=['is_active'])

    status = 'تم تفعيل' if user.is_active else 'تم إلغاء تفعيل'

    return JsonResponse({
        'success': True,
        'message': f'{status} المستخدم {user.username}',
        'is_active': user.is_active
    })

@login_required
@user_passes_test(is_admin_or_staff)
@require_http_methods(["POST"])
def reset_user_password(request, user_id):
    """Reset user password"""
    user = get_object_or_404(User, id=user_id)

    # Generate temporary password
    import secrets
    import string

    alphabet = string.ascii_letters + string.digits
    temp_password = ''.join(secrets.choice(alphabet) for i in range(12))

    user.set_password(temp_password)
    user.last_password_change = timezone.now()
    user.save(update_fields=['password', 'last_password_change'])

    # Send notification with new password
    create_notification(
        user=user,
        title='تم إعادة تعيين كلمة المرور',
        message=f'تم إعادة تعيين كلمة المرور الخاصة بك. كلمة المرور الجديدة: {temp_password}',
        notification_type='security',
        send_email=True
    )

    return JsonResponse({
        'success': True,
        'message': f'تم إعادة تعيين كلمة مرور المستخدم {user.username} وإرسالها عبر البريد الإلكتروني',
        'temp_password': temp_password  # For admin reference
    })
