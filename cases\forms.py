from django import forms
from django.core.exceptions import ValidationError
from django.db import models
from .models import Case, Hearing, HearingDocument, HearingAction, HearingImportantDate, HearingReminder, CourtDecision, Fee
from clients.models import Client
from django.contrib.auth import get_user_model

User = get_user_model()

class CaseForm(forms.ModelForm):
    """Form for creating and editing cases"""

    class Meta:
        model = Case
        fields = ['case_number', 'title', 'description', 'client', 'lawyer', 'start_date', 'end_date', 'status']
        widgets = {
            'case_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم القضية (مثال: 2024/001)'
            }),
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'عنوان القضية'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'وصف مختصر للقضية'
            }),
            'client': forms.Select(attrs={
                'class': 'form-select'
            }),
            'lawyer': forms.Select(attrs={
                'class': 'form-select'
            }),
            'start_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'end_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            })
        }
        labels = {
            'case_number': 'رقم القضية',
            'title': 'عنوان القضية',
            'description': 'وصف القضية',
            'client': 'العميل',
            'lawyer': 'المحامي المسؤول',
            'start_date': 'تاريخ البدء',
            'end_date': 'تاريخ الإغلاق',
            'status': 'حالة القضية'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make end_date optional
        self.fields['end_date'].required = False
        self.fields['description'].required = False

        # Filter lawyers (users who are staff or superuser)
        self.fields['lawyer'].queryset = User.objects.filter(
            models.Q(is_staff=True) | models.Q(is_superuser=True)
        )

    def clean_case_number(self):
        case_number = self.cleaned_data['case_number']
        # Check if case number already exists (excluding current instance)
        existing_case = Case.objects.filter(case_number=case_number)
        if self.instance.pk:
            existing_case = existing_case.exclude(pk=self.instance.pk)

        if existing_case.exists():
            raise ValidationError('رقم القضية موجود مسبقاً')

        return case_number

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and end_date < start_date:
            raise ValidationError('تاريخ الإغلاق يجب أن يكون بعد تاريخ البدء')

        return cleaned_data


class HearingForm(forms.ModelForm):
    """Form for creating and editing hearings"""

    class Meta:
        model = Hearing
        fields = ['date', 'time', 'location', 'notes', 'outcome']
        widgets = {
            'date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'time': forms.TimeInput(attrs={
                'class': 'form-control',
                'type': 'time'
            }),
            'location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مكان الجلسة'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات حول الجلسة'
            }),
            'outcome': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'نتيجة الجلسة (يملأ بعد انتهاء الجلسة)'
            })
        }
        labels = {
            'date': 'تاريخ الجلسة',
            'time': 'وقت الجلسة',
            'location': 'مكان الجلسة',
            'notes': 'ملاحظات',
            'outcome': 'نتيجة الجلسة'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['notes'].required = False
        self.fields['outcome'].required = False


class HearingDocumentForm(forms.ModelForm):
    """Form for uploading hearing documents"""

    class Meta:
        model = HearingDocument
        fields = ['name', 'file', 'notes']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المستند'
            }),
            'file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx,.jpg,.jpeg,.png'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات حول المستند'
            })
        }
        labels = {
            'name': 'اسم المستند',
            'file': 'الملف',
            'notes': 'ملاحظات'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['notes'].required = False


class HearingActionForm(forms.ModelForm):
    """Form for recording hearing actions"""

    class Meta:
        model = HearingAction
        fields = ['action_type', 'description', 'due_date', 'assigned_to', 'status']
        widgets = {
            'action_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف الإجراء المطلوب'
            }),
            'due_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'assigned_to': forms.Select(attrs={
                'class': 'form-select'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            })
        }
        labels = {
            'action_type': 'نوع الإجراء',
            'description': 'وصف الإجراء',
            'due_date': 'تاريخ الاستحقاق',
            'assigned_to': 'مسند إلى',
            'status': 'الحالة'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['assigned_to'].queryset = User.objects.filter(
            models.Q(is_staff=True) | models.Q(is_superuser=True)
        )
        self.fields['due_date'].required = False


class HearingImportantDateForm(forms.ModelForm):
    """Form for recording important dates"""

    class Meta:
        model = HearingImportantDate
        fields = ['date_type', 'date', 'description', 'reminder_days']
        widgets = {
            'date_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف التاريخ المهم'
            }),
            'reminder_days': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'max': '365',
                'placeholder': 'عدد الأيام للتذكير مسبقاً'
            })
        }
        labels = {
            'date_type': 'نوع التاريخ',
            'date': 'التاريخ',
            'description': 'الوصف',
            'reminder_days': 'التذكير قبل (أيام)'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['reminder_days'].required = False


class HearingReminderForm(forms.ModelForm):
    """Form for creating reminders"""

    class Meta:
        model = HearingReminder
        fields = ['reminder_type', 'reminder_date', 'message', 'is_sent']
        widgets = {
            'reminder_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'reminder_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'message': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'نص التذكير'
            }),
            'is_sent': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            })
        }
        labels = {
            'reminder_type': 'نوع التذكير',
            'reminder_date': 'تاريخ ووقت التذكير',
            'message': 'رسالة التذكير',
            'is_sent': 'تم الإرسال'
        }


class CourtDecisionForm(forms.ModelForm):
    """Form for recording court decisions"""

    class Meta:
        model = CourtDecision
        fields = ['decision_type', 'decision_date', 'decision_text', 'file']
        widgets = {
            'decision_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'decision_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'decision_text': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 6,
                'placeholder': 'نص القرار'
            }),
            'file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx'
            })
        }
        labels = {
            'decision_type': 'نوع القرار',
            'decision_date': 'تاريخ القرار',
            'decision_text': 'نص القرار',
            'file': 'ملف القرار'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['file'].required = False


class FeeForm(forms.ModelForm):
    """Form for recording fees"""

    class Meta:
        model = Fee
        fields = ['fee_type', 'amount', 'due_date', 'paid_date', 'status', 'notes']
        widgets = {
            'fee_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': 'المبلغ'
            }),
            'due_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'paid_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات حول الرسوم'
            })
        }
        labels = {
            'fee_type': 'نوع الرسوم',
            'amount': 'المبلغ',
            'due_date': 'تاريخ الاستحقاق',
            'paid_date': 'تاريخ الدفع',
            'status': 'الحالة',
            'notes': 'ملاحظات'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['paid_date'].required = False
        self.fields['notes'].required = False

    def clean(self):
        cleaned_data = super().clean()
        due_date = cleaned_data.get('due_date')
        paid_date = cleaned_data.get('paid_date')
        status = cleaned_data.get('status')

        if status == 'paid' and not paid_date:
            raise ValidationError('يجب تحديد تاريخ الدفع عند تغيير الحالة إلى "مدفوع"')

        if due_date and paid_date and paid_date < due_date:
            # This is just a warning, not an error
            pass

        return cleaned_data


class CaseSearchForm(forms.Form):
    """Form for searching cases"""

    search_query = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'ابحث في رقم القضية، العنوان، أو اسم العميل...'
        }),
        label='البحث'
    )

    status = forms.ChoiceField(
        required=False,
        choices=[('', 'جميع الحالات')] + Case.STATUS_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='الحالة'
    )

    client = forms.ModelChoiceField(
        required=False,
        queryset=Client.objects.all(),
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='العميل'
    )

    lawyer = forms.ModelChoiceField(
        required=False,
        queryset=User.objects.filter(
            models.Q(is_staff=True) | models.Q(is_superuser=True)
        ),
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='المحامي'
    )

    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='من تاريخ'
    )

    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='إلى تاريخ'
    )

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')

        if date_from and date_to and date_to < date_from:
            raise ValidationError('تاريخ النهاية يجب أن يكون بعد تاريخ البداية')

        return cleaned_data