from django import forms
from django.core.exceptions import ValidationError
from django.db import models
from .models import Case, Hearing, HearingDocument, HearingAction, HearingImportantDate, HearingReminder, CourtDecision, Fee
from clients.models import Client
from django.contrib.auth import get_user_model

User = get_user_model()

class CaseForm(forms.ModelForm):
    """Form for creating and editing cases"""

    class Meta:
        model = Case
        fields = ['case_number', 'title', 'description', 'client', 'lawyer', 'start_date', 'end_date', 'status']
        widgets = {
            'case_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم القضية (مثال: 2024/001)'
            }),
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'عنوان القضية'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'وصف مختصر للقضية'
            }),
            'client': forms.Select(attrs={
                'class': 'form-select'
            }),
            'lawyer': forms.Select(attrs={
                'class': 'form-select'
            }),
            'start_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'end_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            })
        }
        labels = {
            'case_number': 'رقم القضية',
            'title': 'عنوان القضية',
            'description': 'وصف القضية',
            'client': 'العميل',
            'lawyer': 'المحامي المسؤول',
            'start_date': 'تاريخ البدء',
            'end_date': 'تاريخ الإغلاق',
            'status': 'حالة القضية'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make end_date optional
        self.fields['end_date'].required = False
        self.fields['description'].required = False

        # Filter lawyers (users who are staff or superuser)
        self.fields['lawyer'].queryset = User.objects.filter(
            models.Q(is_staff=True) | models.Q(is_superuser=True)
        )

    def clean_case_number(self):
        case_number = self.cleaned_data['case_number']
        # Check if case number already exists (excluding current instance)
        existing_case = Case.objects.filter(case_number=case_number)
        if self.instance.pk:
            existing_case = existing_case.exclude(pk=self.instance.pk)

        if existing_case.exists():
            raise ValidationError('رقم القضية موجود مسبقاً')

        return case_number

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and end_date < start_date:
            raise ValidationError('تاريخ الإغلاق يجب أن يكون بعد تاريخ البدء')

        return cleaned_data


class HearingForm(forms.ModelForm):
    """Form for creating and editing hearings"""

    class Meta:
        model = Hearing
        fields = ['date', 'time', 'location', 'notes', 'outcome']
        widgets = {
            'date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'time': forms.TimeInput(attrs={
                'class': 'form-control',
                'type': 'time'
            }),
            'location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مكان الجلسة'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات حول الجلسة'
            }),
            'outcome': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'نتيجة الجلسة (يملأ بعد انتهاء الجلسة)'
            })
        }
        labels = {
            'date': 'تاريخ الجلسة',
            'time': 'وقت الجلسة',
            'location': 'مكان الجلسة',
            'notes': 'ملاحظات',
            'outcome': 'نتيجة الجلسة'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['notes'].required = False
        self.fields['outcome'].required = False


class HearingDocumentForm(forms.ModelForm):
    """Form for uploading hearing documents"""

    class Meta:
        model = HearingDocument
        fields = ['name', 'file', 'notes']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المستند'
            }),
            'file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx,.jpg,.jpeg,.png'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات حول المستند'
            })
        }
        labels = {
            'name': 'اسم المستند',
            'file': 'الملف',
            'notes': 'ملاحظات'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['notes'].required = False


class HearingActionForm(forms.ModelForm):
    """Form for recording hearing actions"""

    class Meta:
        model = HearingAction
        fields = ['action_type', 'description', 'due_date', 'assigned_to', 'status']
        widgets = {
            'action_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف الإجراء المطلوب'
            }),
            'due_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'assigned_to': forms.Select(attrs={
                'class': 'form-select'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            })
        }
        labels = {
            'action_type': 'نوع الإجراء',
            'description': 'وصف الإجراء',
            'due_date': 'تاريخ الاستحقاق',
            'assigned_to': 'مسند إلى',
            'status': 'الحالة'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['assigned_to'].queryset = User.objects.filter(
            models.Q(is_staff=True) | models.Q(is_superuser=True)
        )
        self.fields['due_date'].required = False