{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <h3 class="mb-4 fw-bold"><i class="bi bi-file-earmark-text text-primary me-2"></i>تقرير المستندات</h3>
    <form method="get" class="row g-3 mb-4 align-items-end">
        <div class="col-md-3">
            <label class="form-label">القضية</label>
            <select name="case" class="form-select">
                <option value="">كل القضايا</option>
                {% for case in cases %}
                    <option value="{{ case.id }}" {% if case.id|stringformat:'s' == selected_case %}selected{% endif %}>{{ case.title }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">العميل</label>
            <select name="client" class="form-select">
                <option value="">كل العملاء</option>
                {% for client in clients %}
                    <option value="{{ client.id }}" {% if client.id|stringformat:'s' == selected_client %}selected{% endif %}>{{ client.full_name }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">تاريخ الرفع</label>
            <input type="date" name="date" class="form-control" value="{{ selected_date }}">
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-primary w-100">تصفية</button>
        </div>
    </form>
    <div class="mb-3 d-flex flex-wrap gap-2">
        <a href="{% url 'reports:documents_report_pdf' %}?case={{ selected_case }}&client={{ selected_client }}&date={{ selected_date }}" class="btn btn-outline-danger">تصدير PDF</a>
        <a href="{% url 'reports:documents_report_excel' %}?case={{ selected_case }}&client={{ selected_client }}&date={{ selected_date }}" class="btn btn-outline-success">تصدير Excel</a>
    </div>
    <div class="mb-4">
        <canvas id="documentsChart" height="100"></canvas>
    </div>
    <div class="card p-3 shadow rounded-4 border-0">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>اسم المستند</th>
                        <th>القضية</th>
                        <th>العميل</th>
                        <th>تاريخ الرفع</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for document in documents %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ document.name }}</td>
                        <td>{{ document.case }}</td>
                        <td>{{ document.client }}</td>
                        <td>{{ document.uploaded_at|date:'Y-m-d H:i' }}</td>
                        <td>{{ document.notes }}</td>
                    </tr>
                    {% empty %}
                    <tr><td colspan="6" class="text-center">لا توجد مستندات مطابقة.</td></tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
{% endblock %}
{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // رسم بياني Chart.js
    const chartData = JSON.parse('{{ chart_data|safe|escapejs }}');
    const ctx = document.getElementById('documentsChart').getContext('2d');
    const labels = chartData.map(item => item.label);
    const data = chartData.map(item => item.count);
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'عدد المستندات لكل قضية',
                data: data,
                backgroundColor: '#198754',
            }]
        },
        options: {
            plugins: {
                legend: { display: false }
            },
            scales: {
                y: { beginAtZero: true }
            }
        }
    });
</script>
{% endblock %} 