from django.db import models
from cases.models import Case
from clients.models import Client
from users.models import User

class Appointment(models.Model):
    title = models.CharField(max_length=255, verbose_name='عنوان الموعد')
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='appointments', verbose_name='القضية')
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='appointments', verbose_name='العميل')
    lawyer = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='appointments', verbose_name='المحامي')
    datetime = models.DateTimeField(verbose_name='التاريخ والوقت')
    location = models.CharField(max_length=255, verbose_name='الموقع/رابط الاجتماع', blank=True, null=True)
    notes = models.TextField(verbose_name='ملاحظات', blank=True, null=True)

    def __str__(self):
        return f"{self.title} - {self.datetime}"
