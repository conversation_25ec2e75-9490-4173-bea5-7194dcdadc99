from django.urls import path, include
from .views import (
    CaseListView, CaseCreateView, CaseUpdateView, CaseDetailView, CaseDeleteView, CaseViewSet,
    HearingDetailView, HearingCreateView, HearingDocumentCreateView, HearingActionCreateView, HearingImportantDateCreateView, HearingReminderCreateView, CourtDecisionCreateUpdateView, CourtDecisionDeleteView,
    HearingDocumentUpdateView, HearingDocumentDeleteView, HearingActionUpdateView, HearingActionDeleteView, HearingImportantDateUpdateView, HearingImportantDateDeleteView, HearingReminderUpdateView, HearingReminderDeleteView, FeeCreateView, FeeUpdateView, FeeDeleteView
)
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'cases'

router = DefaultRouter()
router.register(r'api/cases', CaseViewSet, basename='api-cases')

urlpatterns = [
    path('', CaseListView.as_view(), name='list'),
    path('add/', CaseCreateView.as_view(), name='add'),
    path('<int:pk>/edit/', CaseUpdateView.as_view(), name='edit'),
    path('<int:pk>/', CaseDetailView.as_view(), name='detail'),
    path('<int:pk>/delete/', CaseDeleteView.as_view(), name='delete'),
    path('hearing/<int:pk>/', HearingDetailView.as_view(), name='hearing_detail'),
    path('<int:case_pk>/hearing/add/', HearingCreateView.as_view(), name='hearing_add'),
    path('hearing/<int:hearing_pk>/document/add/', HearingDocumentCreateView.as_view(), name='hearingdocument_add'),
    path('hearing/<int:hearing_pk>/action/add/', HearingActionCreateView.as_view(), name='hearingaction_add'),
    path('hearing/<int:hearing_pk>/importantdate/add/', HearingImportantDateCreateView.as_view(), name='hearingimportantdate_add'),
    path('hearing/<int:hearing_pk>/reminder/add/', HearingReminderCreateView.as_view(), name='hearingreminder_add'),
    path('hearing/<int:hearing_pk>/courtdecision/', CourtDecisionCreateUpdateView.as_view(), name='courtdecision_add_or_update'),
    path('hearing/document/<int:pk>/edit/', HearingDocumentUpdateView.as_view(), name='hearingdocument_edit'),
    path('hearing/document/<int:pk>/delete/', HearingDocumentDeleteView.as_view(), name='hearingdocument_delete'),
    path('hearing/action/<int:pk>/edit/', HearingActionUpdateView.as_view(), name='hearingaction_edit'),
    path('hearing/action/<int:pk>/delete/', HearingActionDeleteView.as_view(), name='hearingaction_delete'),
    path('hearing/importantdate/<int:pk>/edit/', HearingImportantDateUpdateView.as_view(), name='hearingimportantdate_edit'),
    path('hearing/importantdate/<int:pk>/delete/', HearingImportantDateDeleteView.as_view(), name='hearingimportantdate_delete'),
    path('hearing/reminder/<int:pk>/edit/', HearingReminderUpdateView.as_view(), name='hearingreminder_edit'),
    path('hearing/reminder/<int:pk>/delete/', HearingReminderDeleteView.as_view(), name='hearingreminder_delete'),
    path('hearing/courtdecision/<int:pk>/delete/', CourtDecisionDeleteView.as_view(), name='courtdecision_delete'),
    path('<int:case_pk>/fee/add/', FeeCreateView.as_view(), name='fee_add'),
    path('fee/<int:pk>/edit/', FeeUpdateView.as_view(), name='fee_edit'),
    path('fee/<int:pk>/delete/', FeeDeleteView.as_view(), name='fee_delete'),
] + router.urls

urlpatterns += [
    path('<int:case_id>/accounts/', views.case_accounts, name='case_accounts'),
    path('<int:case_id>/accounts/add_payment/', views.add_case_payment, name='add_case_payment'),
] 