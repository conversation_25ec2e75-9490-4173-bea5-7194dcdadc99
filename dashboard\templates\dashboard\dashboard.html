{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <h3 class="mb-4 fw-bold"><i class="bi bi-house text-primary me-2"></i>لوحة التحكم</h3>
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <a href="/cases/" class="dashboard-link-card card text-center p-3 border-primary border-2">
                <h5>القضايا</h5>
                <div class="display-5 fw-bold text-primary">{{ cases_count }}</div>
                <div class="small text-muted">إدارة القضايا ومتابعتها</div>
            </a>
        </div>
        <div class="col-md-3">
            <a href="/clients/" class="dashboard-link-card card text-center p-3 border-success border-2">
                <h5>العملاء</h5>
                <div class="display-5 fw-bold text-success">{{ clients_count }}</div>
                <div class="small text-muted">إدارة بيانات العملاء</div>
            </a>
        </div>
        <div class="col-md-3">
            <a href="/appointments/" class="dashboard-link-card card text-center p-3 border-warning border-2">
                <h5>المواعيد</h5>
                <div class="display-5 fw-bold text-warning">{{ appointments_count }}</div>
                <div class="small text-muted">جدولة ومتابعة المواعيد</div>
            </a>
        </div>
        <div class="col-md-3">
            <a href="/documents/" class="dashboard-link-card card text-center p-3 border-info border-2">
                <h5>المستندات</h5>
                <div class="display-5 fw-bold text-info">{{ documents_count }}</div>
                <div class="small text-muted">إدارة المستندات والملفات</div>
            </a>
        </div>
        <div class="col-md-3">
            <a href="/reports/" class="dashboard-link-card card text-center p-3 border-secondary border-2">
                <h5><span class="me-1">التقارير</span> <i class="bi bi-bar-chart"></i></h5>
                <div class="display-6 text-secondary"><i class="bi bi-bar-chart"></i></div>
                <div class="small text-muted">تقارير القضايا والمواعيد والمستندات</div>
            </a>
        </div>
        <div class="col-md-3">
            <a href="/settings/edit/" class="dashboard-link-card card text-center p-3 border-dark border-2">
                <h5><span class="me-1">الإعدادات</span> <i class="bi bi-gear"></i></h5>
                <div class="display-6 text-dark"><i class="bi bi-gear"></i></div>
                <div class="small text-muted">إعدادات النظام المتقدمة</div>
            </a>
        </div>
        <div class="col-md-3">
            <a href="/admin/activitylog/activitylog/" class="dashboard-link-card card text-center p-3 border-danger border-2">
                <h5><span class="me-1">سجل النشاطات</span> <i class="bi bi-clock-history"></i></h5>
                <div class="display-6 text-danger"><i class="bi bi-clock-history"></i></div>
                <div class="small text-muted">متابعة جميع العمليات في النظام</div>
            </a>
        </div>
    </div>
    <div class="row g-4 mt-2">
        <div class="col-md-4">
            <div class="card p-3 mb-3 shadow rounded-4 border-0">
                <h6 class="mb-2">توزيع القضايا حسب الحالة</h6>
                <canvas id="casesChart" height="120"></canvas>
                <ul class="list-group list-group-flush mt-2">
                    <li class="list-group-item">مفتوحة: <b>{{ cases_open }}</b></li>
                    <li class="list-group-item">مغلقة: <b>{{ cases_closed }}</b></li>
                    <li class="list-group-item">معلقة: <b>{{ cases_pending }}</b></li>
                </ul>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card p-3 mb-3 shadow rounded-4 border-0">
                <h6 class="mb-2">مواعيد اليوم/الأسبوع</h6>
                <div>اليوم: <b>{{ appointments_today }}</b></div>
                <div>هذا الأسبوع: <b>{{ appointments_week }}</b></div>
            </div>
            <div class="card p-3 mb-3 shadow rounded-4 border-0">
                <h6 class="mb-2">المستندات المضافة هذا الشهر</h6>
                <div class="display-6">{{ documents_month }}</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card p-3 mb-3 shadow rounded-4 border-0">
                <h6 class="mb-2">آخر 5 قضايا</h6>
                <ul class="list-group list-group-flush">
                    {% for case in last_cases %}
                    <li class="list-group-item small"><b>{{ case.case_number }}</b> - {{ case.title|truncatechars:20 }}</li>
                    {% empty %}<li class="list-group-item text-muted">لا يوجد</li>{% endfor %}
                </ul>
            </div>
            <div class="card p-3 mb-3 shadow rounded-4 border-0">
                <h6 class="mb-2">آخر 5 مواعيد</h6>
                <ul class="list-group list-group-flush">
                    {% for appt in last_appointments %}
                    <li class="list-group-item small">{{ appt.title|truncatechars:20 }} - {{ appt.datetime|date:"Y-m-d H:i" }}</li>
                    {% empty %}<li class="list-group-item text-muted">لا يوجد</li>{% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
<style>
.dashboard-link-card {
    transition: transform 0.15s, box-shadow 0.15s;
    cursor: pointer;
}
.dashboard-link-card:hover {
    transform: translateY(-4px) scale(1.03);
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    background: linear-gradient(90deg, #fffbe6 0%, #f8fafc 100%);
}
</style>
{% endblock %}
{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // رسم بياني لتوزيع القضايا
    new Chart(document.getElementById('casesChart'), {
        type: 'doughnut',
        data: {
            labels: ['مفتوحة', 'مغلقة', 'معلقة'],
            datasets: [{
                data: [{{ cases_open }}, {{ cases_closed }}, {{ cases_pending }}],
                backgroundColor: ['#0d6efd', '#198754', '#ffc107']
            }]
        },
        options: {
            plugins: { legend: { display: true, position: 'bottom' } },
            cutout: '70%'
        }
    });
</script>
{% endblock %} 