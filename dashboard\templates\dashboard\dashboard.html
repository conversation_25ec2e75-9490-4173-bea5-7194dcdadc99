{% extends 'base.html' %}

{% block title %}لوحة التحكم - نظام مكتب المحاماة{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active" aria-current="page">لوحة التحكم</li>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white border-0 shadow-lg">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="card-title mb-2">
                                <i class="bi bi-sun me-2"></i>
                                مرحباً، {{ user.get_full_name|default:user.username }}
                            </h2>
                            <p class="card-text mb-0 opacity-90">
                                مرحباً بك في نظام إدارة مكتب المحاماة. يمكنك من هنا متابعة جميع أعمالك وإدارة قضاياك بكفاءة.
                            </p>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="display-6">
                                <i class="bi bi-briefcase-fill"></i>
                            </div>
                            <small class="d-block mt-2">{{ "now"|date:"l, j F Y" }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Cards -->
    <div class="row g-4 mb-5">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100 dashboard-link-card" onclick="location.href='/cases/'">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-briefcase fs-2 text-primary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">إجمالي القضايا</h6>
                            <h3 class="mb-0 fw-bold">{{ cases_count }}</h3>
                            <div class="d-flex align-items-center mt-2">
                                <small class="text-success me-2">
                                    <i class="bi bi-arrow-up"></i> مفتوحة: {{ cases_open }}
                                </small>
                                <small class="text-muted">مغلقة: {{ cases_closed }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100 dashboard-link-card" onclick="location.href='/clients/'">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-people fs-2 text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">العملاء</h6>
                            <h3 class="mb-0 fw-bold">{{ clients_count }}</h3>
                            <small class="text-muted">عميل مسجل في النظام</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100 dashboard-link-card" onclick="location.href='/appointments/'">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-calendar-event fs-2 text-warning"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">المواعيد</h6>
                            <h3 class="mb-0 fw-bold">{{ appointments_count }}</h3>
                            <div class="d-flex align-items-center mt-2">
                                <small class="text-warning me-2">
                                    <i class="bi bi-clock"></i> اليوم: {{ appointments_today }}
                                </small>
                                <small class="text-muted">الأسبوع: {{ appointments_week }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100 dashboard-link-card" onclick="location.href='/documents/'">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-file-earmark-text fs-2 text-info"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">المستندات</h6>
                            <h3 class="mb-0 fw-bold">{{ documents_count }}</h3>
                            <small class="text-muted">هذا الشهر: {{ documents_month }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Recent Activity Row -->
    <div class="row g-4 mb-4">
        <!-- Cases Status Chart -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pie-chart text-primary me-2"></i>
                        حالة القضايا
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="casesChart" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Monthly Activity Chart -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bar-chart text-success me-2"></i>
                        النشاط الشهري
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="activityChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity and Quick Actions -->
    <div class="row g-4">
        <!-- Recent Cases -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-briefcase text-primary me-2"></i>
                        آخر القضايا
                    </h5>
                    <a href="/cases/" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
                <div class="card-body p-0">
                    {% if last_cases %}
                        <div class="list-group list-group-flush">
                            {% for case in last_cases %}
                            <div class="list-group-item border-0 py-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="/cases/{{ case.pk }}/" class="text-decoration-none">{{ case.title }}</a>
                                        </h6>
                                        <p class="mb-1 text-muted small">{{ case.case_number }}</p>
                                        <small class="text-muted">{{ case.start_date|date:"j F Y" }}</small>
                                    </div>
                                    <span class="badge bg-{% if case.status == 'open' %}success{% elif case.status == 'closed' %}secondary{% elif case.status == 'pending' %}warning{% else %}primary{% endif %} rounded-pill">
                                        {{ case.get_status_display }}
                                    </span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5 text-muted">
                            <i class="bi bi-briefcase display-6 d-block mb-3 opacity-50"></i>
                            <p class="mb-0">لا توجد قضايا بعد</p>
                            <a href="/cases/add/" class="btn btn-primary btn-sm mt-2">إضافة قضية جديدة</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Appointments -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-event text-warning me-2"></i>
                        المواعيد القادمة
                    </h5>
                    <a href="/appointments/" class="btn btn-sm btn-outline-warning">عرض الكل</a>
                </div>
                <div class="card-body p-0">
                    {% if last_appointments %}
                        <div class="list-group list-group-flush">
                            {% for appointment in last_appointments %}
                            <div class="list-group-item border-0 py-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="/appointments/{{ appointment.pk }}/" class="text-decoration-none">{{ appointment.title }}</a>
                                        </h6>
                                        <p class="mb-1 text-muted small">{{ appointment.client.full_name }}</p>
                                        <small class="text-muted">
                                            <i class="bi bi-geo-alt me-1"></i>{{ appointment.location|default:"غير محدد" }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <div class="badge bg-light text-dark">{{ appointment.datetime|date:"j M" }}</div>
                                        <div class="small text-muted mt-1">{{ appointment.datetime|date:"H:i" }}</div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5 text-muted">
                            <i class="bi bi-calendar-x display-6 d-block mb-3 opacity-50"></i>
                            <p class="mb-0">لا توجد مواعيد قادمة</p>
                            <a href="/appointments/add/" class="btn btn-warning btn-sm mt-2">إضافة موعد جديد</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightning text-primary me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="/cases/add/" class="btn btn-outline-primary w-100 py-3">
                                <i class="bi bi-plus-circle d-block fs-4 mb-2"></i>
                                إضافة قضية جديدة
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="/clients/add/" class="btn btn-outline-success w-100 py-3">
                                <i class="bi bi-person-plus d-block fs-4 mb-2"></i>
                                إضافة عميل جديد
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="/appointments/add/" class="btn btn-outline-warning w-100 py-3">
                                <i class="bi bi-calendar-plus d-block fs-4 mb-2"></i>
                                جدولة موعد
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="/documents/add/" class="btn btn-outline-info w-100 py-3">
                                <i class="bi bi-file-plus d-block fs-4 mb-2"></i>
                                رفع مستند
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #3d4a6b 100%) !important;
}

.dashboard-link-card {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(0,0,0,0.05) !important;
}

.dashboard-link-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.list-group-item {
    transition: background-color 0.2s ease;
}

.list-group-item:hover {
    background-color: rgba(0,0,0,0.02);
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 8px 30px rgba(0,0,0,0.1) !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Cases Status Chart
    const casesCtx = document.getElementById('casesChart').getContext('2d');
    new Chart(casesCtx, {
        type: 'doughnut',
        data: {
            labels: ['مفتوحة', 'مغلقة', 'معلقة'],
            datasets: [{
                data: [{{ cases_open }}, {{ cases_closed }}, {{ cases_pending }}],
                backgroundColor: ['#28a745', '#6c757d', '#ffc107'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Activity Chart (placeholder data)
    const activityCtx = document.getElementById('activityChart').getContext('2d');
    new Chart(activityCtx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'القضايا الجديدة',
                data: [12, 19, 3, 5, 2, 3],
                borderColor: '#0d6efd',
                backgroundColor: 'rgba(13, 110, 253, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
{% endblock %}
                <div class="display-6 text-danger"><i class="bi bi-clock-history"></i></div>
                <div class="small text-muted">متابعة جميع العمليات في النظام</div>
            </a>
        </div>
    </div>
    <div class="row g-4 mt-2">
        <div class="col-md-4">
            <div class="card p-3 mb-3 shadow rounded-4 border-0">
                <h6 class="mb-2">توزيع القضايا حسب الحالة</h6>
                <canvas id="casesChart" height="120"></canvas>
                <ul class="list-group list-group-flush mt-2">
                    <li class="list-group-item">مفتوحة: <b>{{ cases_open }}</b></li>
                    <li class="list-group-item">مغلقة: <b>{{ cases_closed }}</b></li>
                    <li class="list-group-item">معلقة: <b>{{ cases_pending }}</b></li>
                </ul>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card p-3 mb-3 shadow rounded-4 border-0">
                <h6 class="mb-2">مواعيد اليوم/الأسبوع</h6>
                <div>اليوم: <b>{{ appointments_today }}</b></div>
                <div>هذا الأسبوع: <b>{{ appointments_week }}</b></div>
            </div>
            <div class="card p-3 mb-3 shadow rounded-4 border-0">
                <h6 class="mb-2">المستندات المضافة هذا الشهر</h6>
                <div class="display-6">{{ documents_month }}</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card p-3 mb-3 shadow rounded-4 border-0">
                <h6 class="mb-2">آخر 5 قضايا</h6>
                <ul class="list-group list-group-flush">
                    {% for case in last_cases %}
                    <li class="list-group-item small"><b>{{ case.case_number }}</b> - {{ case.title|truncatechars:20 }}</li>
                    {% empty %}<li class="list-group-item text-muted">لا يوجد</li>{% endfor %}
                </ul>
            </div>
            <div class="card p-3 mb-3 shadow rounded-4 border-0">
                <h6 class="mb-2">آخر 5 مواعيد</h6>
                <ul class="list-group list-group-flush">
                    {% for appt in last_appointments %}
                    <li class="list-group-item small">{{ appt.title|truncatechars:20 }} - {{ appt.datetime|date:"Y-m-d H:i" }}</li>
                    {% empty %}<li class="list-group-item text-muted">لا يوجد</li>{% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
<style>
.dashboard-link-card {
    transition: transform 0.15s, box-shadow 0.15s;
    cursor: pointer;
}
.dashboard-link-card:hover {
    transform: translateY(-4px) scale(1.03);
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    background: linear-gradient(90deg, #fffbe6 0%, #f8fafc 100%);
}
</style>
{% endblock %}
{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // رسم بياني لتوزيع القضايا
    new Chart(document.getElementById('casesChart'), {
        type: 'doughnut',
        data: {
            labels: ['مفتوحة', 'مغلقة', 'معلقة'],
            datasets: [{
                data: [{{ cases_open }}, {{ cases_closed }}, {{ cases_pending }}],
                backgroundColor: ['#0d6efd', '#198754', '#ffc107']
            }]
        },
        options: {
            plugins: { legend: { display: true, position: 'bottom' } },
            cutout: '70%'
        }
    });
</script>
{% endblock %} 