{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card p-4 mt-4 shadow rounded-4 border-0">
                <h4 class="mb-3 fw-bold"><i class="bi bi-search text-primary me-2"></i>البحث المتقدم</h4>
                <form method="get" class="mb-4">
                    <div class="row g-2 align-items-end">
                        <div class="col-md-3">
                            <label class="form-label">نوع الكيان</label>
                            <select name="entity" class="form-select" onchange="this.form.submit()">
                                <option value="">اختر...</option>
                                {% for key, label in entity_labels.items %}
                                    <option value="{{ key }}" {% if entity == key %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        {% if entity == 'case' %}
                            <div class="col-md-2">
                                <label class="form-label">رقم القضية</label>
                                <input type="text" name="case_number" value="{{ filters.case_number }}" class="form-control">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">عنوان القضية</label>
                                <input type="text" name="title" value="{{ filters.title }}" class="form-control">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select name="status" class="form-select">
                                    <option value="">الكل</option>
                                    <option value="open" {% if filters.status == 'open' %}selected{% endif %}>مفتوحة</option>
                                    <option value="closed" {% if filters.status == 'closed' %}selected{% endif %}>مغلقة</option>
                                    <option value="pending" {% if filters.status == 'pending' %}selected{% endif %}>معلقة</option>
                                </select>
                            </div>
                        {% elif entity == 'client' %}
                            <div class="col-md-4">
                                <label class="form-label">اسم العميل</label>
                                <input type="text" name="full_name" value="{{ filters.full_name }}" class="form-control">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">رقم الهوية</label>
                                <input type="text" name="national_id" value="{{ filters.national_id }}" class="form-control">
                            </div>
                        {% elif entity == 'document' %}
                            <div class="col-md-6">
                                <label class="form-label">اسم المستند</label>
                                <input type="text" name="name" value="{{ filters.name }}" class="form-control">
                            </div>
                        {% elif entity == 'appointment' %}
                            <div class="col-md-6">
                                <label class="form-label">عنوان الموعد</label>
                                <input type="text" name="title" value="{{ filters.title }}" class="form-control">
                            </div>
                        {% endif %}
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-gradient-gold w-100">بحث</button>
                        </div>
                    </div>
                </form>
                {% if results is not None %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="table-light">
                                <tr>
                                    {% if entity == 'case' %}
                                        <th>رقم القضية</th><th>العنوان</th><th>الحالة</th><th>العميل</th><th>المحامي</th>
                                    {% elif entity == 'client' %}
                                        <th>الاسم الكامل</th><th>رقم الهوية</th><th>رقم الهاتف</th><th>البريد الإلكتروني</th>
                                    {% elif entity == 'document' %}
                                        <th>اسم المستند</th><th>القضية</th><th>العميل</th><th>تاريخ الرفع</th>
                                    {% elif entity == 'appointment' %}
                                        <th>عنوان الموعد</th><th>القضية</th><th>العميل</th><th>المحامي</th><th>التاريخ والوقت</th>
                                    {% endif %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for obj in results %}
                                    <tr>
                                        {% if entity == 'case' %}
                                            <td>{{ obj.case_number }}</td><td>{{ obj.title }}</td><td>{{ obj.get_status_display }}</td><td>{{ obj.client }}</td><td>{{ obj.lawyer }}</td>
                                        {% elif entity == 'client' %}
                                            <td>{{ obj.full_name }}</td><td>{{ obj.national_id }}</td><td>{{ obj.phone }}</td><td>{{ obj.email }}</td>
                                        {% elif entity == 'document' %}
                                            <td>{{ obj.name }}</td><td>{{ obj.case }}</td><td>{{ obj.client }}</td><td>{{ obj.uploaded_at|date:"Y-m-d H:i" }}</td>
                                        {% elif entity == 'appointment' %}
                                            <td>{{ obj.title }}</td><td>{{ obj.case }}</td><td>{{ obj.client }}</td><td>{{ obj.lawyer }}</td><td>{{ obj.datetime|date:"Y-m-d H:i" }}</td>
                                        {% endif %}
                                    </tr>
                                {% empty %}
                                    <tr><td colspan="8" class="text-center">لا توجد نتائج.</td></tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
<style>
.btn-gradient-gold {
    background: linear-gradient(90deg, #ffd600 0%, #ffb300 100%);
    color: #222b45;
    border: none;
    font-weight: bold;
    transition: box-shadow 0.2s;
}
.btn-gradient-gold:hover {
    box-shadow: 0 2px 12px #ffd60055;
    color: #151a30;
}
</style>
{% endblock %} 