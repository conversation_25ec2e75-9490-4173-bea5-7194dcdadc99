from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import FileExtensionValidator
from django.utils import timezone
from cases.models import Case
from clients.models import Client
from .utils import get_file_upload_path, validate_file, get_file_info
import os

User = get_user_model()

class DocumentCategory(models.Model):
    """Document categories for better organization"""
    name = models.CharField(max_length=100, verbose_name='اسم الفئة')
    description = models.TextField(blank=True, verbose_name='الوصف')
    color = models.CharField(max_length=7, default='#007bff', verbose_name='اللون')
    icon = models.CharField(max_length=50, default='bi-file-text', verbose_name='الأيقونة')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'فئة المستند'
        verbose_name_plural = 'فئات المستندات'
        ordering = ['name']

    def __str__(self):
        return self.name

class Document(models.Model):
    """Enhanced document model with better file management"""

    DOCUMENT_TYPES = [
        ('contract', 'عقد'),
        ('court_document', 'مستند محكمة'),
        ('evidence', 'دليل'),
        ('correspondence', 'مراسلات'),
        ('invoice', 'فاتورة'),
        ('receipt', 'إيصال'),
        ('report', 'تقرير'),
        ('other', 'أخرى'),
    ]

    ACCESS_LEVELS = [
        ('public', 'عام'),
        ('private', 'خاص'),
        ('confidential', 'سري'),
    ]

    name = models.CharField(max_length=255, verbose_name='اسم المستند')
    description = models.TextField(blank=True, verbose_name='الوصف')
    file = models.FileField(
        upload_to=get_file_upload_path,
        verbose_name='الملف',
        validators=[FileExtensionValidator(
            allowed_extensions=['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'jpg', 'jpeg', 'png', 'gif']
        )]
    )
    thumbnail = models.ImageField(upload_to='documents/thumbnails/', blank=True, null=True, verbose_name='الصورة المصغرة')

    # Relationships
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='documents', verbose_name='القضية', blank=True, null=True)
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='documents', verbose_name='العميل', blank=True, null=True)
    category = models.ForeignKey(DocumentCategory, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='الفئة')
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='رفع بواسطة')

    # Document properties
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES, default='other', verbose_name='نوع المستند')
    access_level = models.CharField(max_length=20, choices=ACCESS_LEVELS, default='private', verbose_name='مستوى الوصول')
    file_size = models.PositiveIntegerField(default=0, verbose_name='حجم الملف')
    file_type = models.CharField(max_length=100, blank=True, verbose_name='نوع الملف')

    # Timestamps
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    # Additional fields
    notes = models.TextField(blank=True, verbose_name='ملاحظات')
    tags = models.CharField(max_length=500, blank=True, verbose_name='العلامات', help_text='افصل بين العلامات بفاصلة')
    is_archived = models.BooleanField(default=False, verbose_name='مؤرشف')
    version = models.PositiveIntegerField(default=1, verbose_name='الإصدار')

    class Meta:
        verbose_name = 'مستند'
        verbose_name_plural = 'المستندات'
        ordering = ['-uploaded_at']
        indexes = [
            models.Index(fields=['case', 'uploaded_at']),
            models.Index(fields=['client', 'uploaded_at']),
            models.Index(fields=['document_type', 'uploaded_at']),
        ]

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # Set file size and type before saving
        if self.file:
            self.file_size = self.file.size
            self.file_type = self.file.content_type or ''

        super().save(*args, **kwargs)

    @property
    def file_extension(self):
        """Get file extension"""
        if self.file:
            return os.path.splitext(self.file.name)[1].lower()
        return ''

    @property
    def file_size_human(self):
        """Get human readable file size"""
        from .utils import format_file_size
        return format_file_size(self.file_size)

    @property
    def is_image(self):
        """Check if file is an image"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
        return self.file_extension in image_extensions

    @property
    def is_pdf(self):
        """Check if file is a PDF"""
        return self.file_extension == '.pdf'

    @property
    def can_preview(self):
        """Check if file can be previewed"""
        return self.is_image or self.is_pdf

    def get_tags_list(self):
        """Get tags as a list"""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        return []

    def get_absolute_url(self):
        """Get absolute URL for the document"""
        return f"/documents/{self.pk}/"

    def delete(self, *args, **kwargs):
        """Override delete to remove file from storage"""
        if self.file:
            from .utils import delete_file_safely
            delete_file_safely(self.file.name)

        if self.thumbnail:
            delete_file_safely(self.thumbnail.name)

        super().delete(*args, **kwargs)


class DocumentVersion(models.Model):
    """Track document versions"""
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='versions', verbose_name='المستند')
    file = models.FileField(upload_to=get_file_upload_path, verbose_name='الملف')
    version_number = models.PositiveIntegerField(verbose_name='رقم الإصدار')
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='رفع بواسطة')
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')
    notes = models.TextField(blank=True, verbose_name='ملاحظات الإصدار')

    class Meta:
        verbose_name = 'إصدار المستند'
        verbose_name_plural = 'إصدارات المستندات'
        ordering = ['-version_number']
        unique_together = ['document', 'version_number']

    def __str__(self):
        return f"{self.document.name} - الإصدار {self.version_number}"


class DocumentAccess(models.Model):
    """Track document access logs"""
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='access_logs', verbose_name='المستند')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    accessed_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الوصول')
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name='عنوان IP')
    user_agent = models.TextField(blank=True, verbose_name='معلومات المتصفح')

    class Meta:
        verbose_name = 'سجل الوصول للمستند'
        verbose_name_plural = 'سجلات الوصول للمستندات'
        ordering = ['-accessed_at']

    def __str__(self):
        return f"{self.user.username} - {self.document.name} - {self.accessed_at}"
