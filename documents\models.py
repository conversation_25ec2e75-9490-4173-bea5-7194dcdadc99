from django.db import models
from cases.models import Case
from clients.models import Client

class Document(models.Model):
    name = models.CharField(max_length=255, verbose_name='اسم المستند')
    file = models.FileField(upload_to='documents/', verbose_name='الملف')
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='documents', verbose_name='القضية', blank=True, null=True)
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='documents', verbose_name='العميل', blank=True, null=True)
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')
    notes = models.TextField(verbose_name='ملاحظات', blank=True, null=True)

    def __str__(self):
        return self.name
