from .models import Notification
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.utils import timezone
from datetime import timedelta
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

def create_notification(user, title, message, url=None, notification_type='general', send_email=False):
    """
    إنشاء إشعار جديد مع إمكانية الإرسال عبر البريد الإلكتروني
    """
    try:
        notification = Notification.objects.create(
            user=user,
            title=title,
            message=message,
            url=url,
            notification_type=notification_type
        )

        # إرسال بريد إلكتروني إذا كان مطلوباً
        if send_email and user.email:
            send_notification_email(user, notification)

        logger.info(f"Notification created for user {user.username}: {title}")
        return notification

    except Exception as e:
        logger.error(f"Error creating notification: {str(e)}")
        return None

def send_notification_email(user, notification):
    """
    إرسال الإشعار عبر البريد الإلكتروني
    """
    try:
        subject = f"إشعار من نظام مكتب المحاماة: {notification.title}"

        # إنشاء محتوى البريد الإلكتروني
        email_message = notification.message
        if notification.url:
            site_url = getattr(settings, 'SITE_URL', 'http://localhost:8000')
            email_message += f"\n\nرابط: {site_url}{notification.url}"

        send_mail(
            subject=subject,
            message=email_message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', None),
            recipient_list=[user.email],
            fail_silently=False
        )

        logger.info(f"Email notification sent to {user.email}")
        return True

    except Exception as e:
        logger.error(f"Error sending email notification: {str(e)}")
        return False

def notify_all_staff(title, message, url=None, notification_type='general', send_email=False):
    """
    إرسال إشعار لجميع الموظفين
    """
    staff_users = User.objects.filter(is_staff=True, is_active=True)
    notifications = []

    for user in staff_users:
        notification = create_notification(
            user, title, message, url, notification_type, send_email
        )
        if notification:
            notifications.append(notification)

    logger.info(f"Notifications sent to {len(notifications)} staff members")
    return notifications

def notify_case_participants(case, title, message, url=None, notification_type='case', send_email=False):
    """
    إرسال إشعار لجميع المشاركين في القضية
    """
    notifications = []

    # إشعار المحامي المسؤول
    if case.lawyer:
        notification = create_notification(
            case.lawyer, title, message, url, notification_type, send_email
        )
        if notification:
            notifications.append(notification)

    return notifications

def create_case_notification(case, action, user=None):
    """
    إنشاء إشعارات خاصة بالقضايا
    """
    notifications = []

    if action == 'created':
        title = f"قضية جديدة: {case.title}"
        message = f"تم إنشاء قضية جديدة برقم {case.case_number} للعميل {case.client.full_name}"
        url = f"/cases/{case.pk}/"

        # إشعار جميع الموظفين
        notifications.extend(notify_all_staff(title, message, url, 'case', True))

    elif action == 'updated':
        title = f"تحديث القضية: {case.title}"
        message = f"تم تحديث القضية رقم {case.case_number}"
        url = f"/cases/{case.pk}/"

        # إشعار المشاركين في القضية
        notifications.extend(notify_case_participants(case, title, message, url, 'case', True))

    elif action == 'status_changed':
        title = f"تغيير حالة القضية: {case.title}"
        message = f"تم تغيير حالة القضية رقم {case.case_number} إلى {case.get_status_display()}"
        url = f"/cases/{case.pk}/"

        # إشعار المشاركين في القضية
        notifications.extend(notify_case_participants(case, title, message, url, 'case', True))

    return notifications

def create_appointment_notification(appointment, action, user=None):
    """
    إنشاء إشعارات خاصة بالمواعيد
    """
    notifications = []

    if action == 'created':
        title = f"موعد جديد: {appointment.title}"
        message = f"تم جدولة موعد جديد مع {appointment.client.full_name} في {appointment.datetime.strftime('%Y-%m-%d %H:%M')}"
        url = f"/appointments/{appointment.pk}/"

        # إشعار المحامي
        if appointment.lawyer:
            notification = create_notification(
                appointment.lawyer, title, message, url, 'appointment', True
            )
            if notification:
                notifications.append(notification)

    elif action == 'reminder':
        title = f"تذكير بالموعد: {appointment.title}"
        message = f"لديك موعد مع {appointment.client.full_name} في {appointment.datetime.strftime('%Y-%m-%d %H:%M')}"
        url = f"/appointments/{appointment.pk}/"

        # إشعار المحامي فقط
        if appointment.lawyer:
            notification = create_notification(
                appointment.lawyer, title, message, url, 'appointment', True
            )
            if notification:
                notifications.append(notification)

    return notifications

def mark_notifications_as_read(user, notification_ids=None):
    """
    تحديد الإشعارات كمقروءة
    """
    queryset = Notification.objects.filter(user=user, is_read=False)

    if notification_ids:
        queryset = queryset.filter(id__in=notification_ids)

    updated_count = queryset.update(is_read=True)
    logger.info(f"Marked {updated_count} notifications as read for user {user.username}")

    return updated_count

def get_unread_count(user):
    """
    الحصول على عدد الإشعارات غير المقروءة
    """
    return Notification.objects.filter(user=user, is_read=False).count()