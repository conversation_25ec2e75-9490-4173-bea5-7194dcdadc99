from .models import Notification
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.conf import settings

def create_notification(user, title, message, url=None, notification_type='general', send_email=False):
    if user:
        Notification.objects.create(
            user=user,
            title=title,
            message=message,
            url=url,
            notification_type=notification_type
        )
        if send_email and user.email:
            email_message = message
            if url:
                email_message += f"\nرابط: {settings.SITE_URL if hasattr(settings, 'SITE_URL') else ''}{url}"
            send_mail(
                subject=title,
                message=email_message,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', None),
                recipient_list=[user.email],
                fail_silently=True,
            ) 