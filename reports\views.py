from django.shortcuts import render
from django.http import HttpResponse
from cases.models import Case
import io
import pandas as pd
from django.template.loader import render_to_string
from xhtml2pdf import pisa
from appointments.models import Appointment
from users.models import User
from django.utils import timezone
from documents.models import Document
from clients.models import Client
from cases.models import Fee, Case
from django.views.generic import TemplateView
import openpyxl
from weasyprint import HTML
from django.db.models import Sum

def reports_home(request):
    return render(request, 'reports/reports_home.html')

def cases_report(request):
    status = request.GET.get('status')
    cases = Case.objects.all()
    if status:
        cases = cases.filter(status=status)
    statuses = Case._meta.get_field('status').choices
    # بيانات الرسم البياني
    chart_data = []
    for value, label in statuses:
        count = Case.objects.filter(status=value).count()
        chart_data.append({'label': label, 'count': count})
    return render(request, 'reports/cases_report.html', {
        'cases': cases,
        'statuses': statuses,
        'selected_status': status,
        'chart_data': chart_data,
    })

def cases_report_pdf(request):
    status = request.GET.get('status')
    cases = Case.objects.all()
    if status:
        cases = cases.filter(status=status)
    html = render_to_string('reports/cases_report_pdf.html', {'cases': cases})
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename="cases_report.pdf"'
    pisa.CreatePDF(io.BytesIO(html.encode('utf-8')), dest=response)
    return response

def cases_report_excel(request):
    status = request.GET.get('status')
    cases = Case.objects.all()
    if status:
        cases = cases.filter(status=status)
    data = [
        [c.case_number, c.title, str(c.client), str(c.lawyer), c.start_date, c.get_status_display()]
        for c in cases
    ]
    df = pd.DataFrame(data, columns=['رقم القضية', 'العنوان', 'العميل', 'المحامي', 'تاريخ البدء', 'الحالة'])
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Cases')
    output.seek(0)
    response = HttpResponse(output, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="cases_report.xlsx"'
    return response

def appointments_report(request):
    lawyer_id = request.GET.get('lawyer')
    case_id = request.GET.get('case')
    date = request.GET.get('date')
    appointments = Appointment.objects.all()
    if lawyer_id:
        appointments = appointments.filter(lawyer_id=lawyer_id)
    if case_id:
        appointments = appointments.filter(case_id=case_id)
    if date:
        appointments = appointments.filter(datetime__date=date)
    lawyers = User.objects.all()
    cases = Case.objects.all()
    # بيانات الرسم البياني (عدد المواعيد لكل محامي)
    chart_data = []
    for lawyer in lawyers:
        count = appointments.filter(lawyer=lawyer).count()
        chart_data.append({'label': str(lawyer), 'count': count})
    return render(request, 'reports/appointments_report.html', {
        'appointments': appointments,
        'lawyers': lawyers,
        'cases': cases,
        'selected_lawyer': lawyer_id,
        'selected_case': case_id,
        'selected_date': date,
        'chart_data': chart_data,
    })

def appointments_report_pdf(request):
    lawyer_id = request.GET.get('lawyer')
    case_id = request.GET.get('case')
    date = request.GET.get('date')
    appointments = Appointment.objects.all()
    if lawyer_id:
        appointments = appointments.filter(lawyer_id=lawyer_id)
    if case_id:
        appointments = appointments.filter(case_id=case_id)
    if date:
        appointments = appointments.filter(datetime__date=date)
    html = render_to_string('reports/appointments_report_pdf.html', {'appointments': appointments})
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename="appointments_report.pdf"'
    pisa.CreatePDF(io.BytesIO(html.encode('utf-8')), dest=response)
    return response

def appointments_report_excel(request):
    lawyer_id = request.GET.get('lawyer')
    case_id = request.GET.get('case')
    date = request.GET.get('date')
    appointments = Appointment.objects.all()
    if lawyer_id:
        appointments = appointments.filter(lawyer_id=lawyer_id)
    if case_id:
        appointments = appointments.filter(case_id=case_id)
    if date:
        appointments = appointments.filter(datetime__date=date)
    data = [
        [a.title, str(a.case), str(a.client), str(a.lawyer), a.datetime, a.location, a.notes]
        for a in appointments
    ]
    df = pd.DataFrame(data, columns=['العنوان', 'القضية', 'العميل', 'المحامي', 'التاريخ والوقت', 'الموقع', 'ملاحظات'])
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Appointments')
    output.seek(0)
    response = HttpResponse(output, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="appointments_report.xlsx"'
    return response

def documents_report(request):
    case_id = request.GET.get('case')
    client_id = request.GET.get('client')
    date = request.GET.get('date')
    documents = Document.objects.all()
    if case_id:
        documents = documents.filter(case_id=case_id)
    if client_id:
        documents = documents.filter(client_id=client_id)
    if date:
        documents = documents.filter(uploaded_at__date=date)
    cases = Case.objects.all()
    clients = Client.objects.all()
    # بيانات الرسم البياني (عدد المستندات لكل قضية)
    chart_data = []
    for case in cases:
        count = documents.filter(case=case).count()
        chart_data.append({'label': str(case), 'count': count})
    return render(request, 'reports/documents_report.html', {
        'documents': documents,
        'cases': cases,
        'clients': clients,
        'selected_case': case_id,
        'selected_client': client_id,
        'selected_date': date,
        'chart_data': chart_data,
    })

def documents_report_pdf(request):
    case_id = request.GET.get('case')
    client_id = request.GET.get('client')
    date = request.GET.get('date')
    documents = Document.objects.all()
    if case_id:
        documents = documents.filter(case_id=case_id)
    if client_id:
        documents = documents.filter(client_id=client_id)
    if date:
        documents = documents.filter(uploaded_at__date=date)
    html = render_to_string('reports/documents_report_pdf.html', {'documents': documents})
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename="documents_report.pdf"'
    pisa.CreatePDF(io.BytesIO(html.encode('utf-8')), dest=response)
    return response

def documents_report_excel(request):
    case_id = request.GET.get('case')
    client_id = request.GET.get('client')
    date = request.GET.get('date')
    documents = Document.objects.all()
    if case_id:
        documents = documents.filter(case_id=case_id)
    if client_id:
        documents = documents.filter(client_id=client_id)
    if date:
        documents = documents.filter(uploaded_at__date=date)
    data = [
        [d.name, str(d.case), str(d.client), d.uploaded_at, d.notes]
        for d in documents
    ]
    df = pd.DataFrame(data, columns=['اسم المستند', 'القضية', 'العميل', 'تاريخ الرفع', 'ملاحظات'])
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Documents')
    output.seek(0)
    response = HttpResponse(output, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="documents_report.xlsx"'
    return response

class ReportAccountsSummaryView(TemplateView):
    template_name = 'reports/accounts_summary.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        clients = Client.objects.all()
        clients_data = []
        total_paid = 0
        total_unpaid = 0
        for client in clients:
            cases = client.cases.all()
            total = 0
            for case in cases:
                fees = case.fees.all()
                total_case = sum(f.amount for f in fees)
                total += total_case
            paid = client.payments.aggregate(total_paid=Sum('amount'))['total_paid'] or 0
            unpaid = total - paid
            clients_data.append({
                'client': client,
                'paid': paid,
                'unpaid': unpaid,
                'total': total,
            })
            total_paid += paid
            total_unpaid += unpaid
        context['clients_data'] = clients_data
        context['total_paid'] = total_paid
        context['total_unpaid'] = total_unpaid
        context['total_fees'] = total_paid + total_unpaid
        return context

def accounts_summary_excel(request):
    from clients.models import Client
    from django.db.models import Sum
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = 'تقرير الحسابات'
    ws.append(['اسم العميل', 'إجمالي الأتعاب', 'المدفوع', 'المتبقي'])
    clients = Client.objects.all()
    for client in clients:
        cases = client.cases.all()
        total = 0
        for case in cases:
            fees = case.fees.all()
            total_case = sum(f.amount for f in fees)
            total += total_case
        paid = client.payments.aggregate(total_paid=Sum('amount'))['total_paid'] or 0
        unpaid = total - paid
        ws.append([client.full_name, float(total), float(paid), float(unpaid)])
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename=accounts_summary.xlsx'
    wb.save(response)
    return response

def accounts_summary_pdf(request):
    from clients.models import Client
    from django.db.models import Sum
    clients = Client.objects.all()
    clients_data = []
    total_paid = 0
    total_unpaid = 0
    for client in clients:
        cases = client.cases.all()
        total = 0
        for case in cases:
            fees = case.fees.all()
            total_case = sum(f.amount for f in fees)
            total += total_case
        paid = client.payments.aggregate(total_paid=Sum('amount'))['total_paid'] or 0
        unpaid = total - paid
        clients_data.append({
            'client': client,
            'paid': paid,
            'unpaid': unpaid,
            'total': total,
        })
        total_paid += paid
        total_unpaid += unpaid
    context = {
        'clients_data': clients_data,
        'total_paid': total_paid,
        'total_unpaid': total_unpaid,
        'total_fees': total_paid + total_unpaid,
    }
    html_string = render_to_string('reports/accounts_summary_pdf.html', context)
    pdf_file = HTML(string=html_string).write_pdf()
    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename=accounts_summary.pdf'
    return response
