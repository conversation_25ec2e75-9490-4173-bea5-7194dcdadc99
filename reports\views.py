from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db.models import Sum, Count, Q, Avg
from django.template.loader import render_to_string
from django.views.generic import TemplateView
from datetime import datetime, timedelta
import io
import json
import pandas as pd
import openpyxl
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

# Models
from cases.models import Case, Fee, Hearing
from clients.models import Client, Payment
from appointments.models import Appointment
from documents.models import Document
from notifications.models import Notification

User = get_user_model()

@login_required
def reports_home(request):
    """Enhanced reports home with dashboard statistics"""
    today = timezone.now().date()
    current_month = today.replace(day=1)
    last_month = (current_month - timedelta(days=1)).replace(day=1)

    # Basic statistics
    stats = {
        'total_cases': Case.objects.count(),
        'active_cases': Case.objects.filter(status='open').count(),
        'total_clients': Client.objects.count(),
        'total_appointments': Appointment.objects.count(),
        'total_documents': Document.objects.count(),

        # Monthly comparisons
        'cases_this_month': Case.objects.filter(start_date__gte=current_month).count(),
        'cases_last_month': Case.objects.filter(
            start_date__gte=last_month,
            start_date__lt=current_month
        ).count(),

        'appointments_this_month': Appointment.objects.filter(
            datetime__date__gte=current_month
        ).count(),
        'appointments_last_month': Appointment.objects.filter(
            datetime__date__gte=last_month,
            datetime__date__lt=current_month
        ).count(),

        # Financial data
        'total_fees': Fee.objects.aggregate(total=Sum('amount'))['total'] or 0,
        'paid_fees': Fee.objects.filter(status='paid').aggregate(total=Sum('amount'))['total'] or 0,
        'pending_fees': Fee.objects.filter(status='pending').aggregate(total=Sum('amount'))['total'] or 0,

        'total_payments': Payment.objects.aggregate(total=Sum('amount'))['total'] or 0,
        'payments_this_month': Payment.objects.filter(
            date__gte=current_month
        ).aggregate(total=Sum('amount'))['total'] or 0,
    }

    # Calculate growth percentages
    stats['cases_growth'] = calculate_growth_percentage(
        stats['cases_this_month'],
        stats['cases_last_month']
    )
    stats['appointments_growth'] = calculate_growth_percentage(
        stats['appointments_this_month'],
        stats['appointments_last_month']
    )

    # Recent activity
    recent_cases = Case.objects.select_related('client', 'lawyer').order_by('-id')[:5]
    recent_appointments = Appointment.objects.select_related('client', 'lawyer').order_by('-datetime')[:5]
    recent_documents = Document.objects.select_related('case', 'client').order_by('-uploaded_at')[:5]

    # Chart data for dashboard
    case_status_data = [
        {'label': 'مفتوحة', 'value': Case.objects.filter(status='open').count()},
        {'label': 'مغلقة', 'value': Case.objects.filter(status='closed').count()},
        {'label': 'معلقة', 'value': Case.objects.filter(status='pending').count()},
    ]

    context = {
        'stats': stats,
        'recent_cases': recent_cases,
        'recent_appointments': recent_appointments,
        'recent_documents': recent_documents,
        'case_status_data': json.dumps(case_status_data),
    }

    return render(request, 'reports/reports_home.html', context)

def calculate_growth_percentage(current, previous):
    """Calculate growth percentage between two values"""
    if previous == 0:
        return 100 if current > 0 else 0
    return round(((current - previous) / previous) * 100, 1)

def cases_report(request):
    status = request.GET.get('status')
    cases = Case.objects.all()
    if status:
        cases = cases.filter(status=status)
    statuses = Case._meta.get_field('status').choices
    # بيانات الرسم البياني
    chart_data = []
    for value, label in statuses:
        count = Case.objects.filter(status=value).count()
        chart_data.append({'label': label, 'count': count})
    return render(request, 'reports/cases_report.html', {
        'cases': cases,
        'statuses': statuses,
        'selected_status': status,
        'chart_data': chart_data,
    })

def cases_report_pdf(request):
    status = request.GET.get('status')
    cases = Case.objects.all()
    if status:
        cases = cases.filter(status=status)
    html = render_to_string('reports/cases_report_pdf.html', {'cases': cases})
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename="cases_report.pdf"'
    pisa.CreatePDF(io.BytesIO(html.encode('utf-8')), dest=response)
    return response

def cases_report_excel(request):
    status = request.GET.get('status')
    cases = Case.objects.all()
    if status:
        cases = cases.filter(status=status)
    data = [
        [c.case_number, c.title, str(c.client), str(c.lawyer), c.start_date, c.get_status_display()]
        for c in cases
    ]
    df = pd.DataFrame(data, columns=['رقم القضية', 'العنوان', 'العميل', 'المحامي', 'تاريخ البدء', 'الحالة'])
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Cases')
    output.seek(0)
    response = HttpResponse(output, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="cases_report.xlsx"'
    return response

def appointments_report(request):
    lawyer_id = request.GET.get('lawyer')
    case_id = request.GET.get('case')
    date = request.GET.get('date')
    appointments = Appointment.objects.all()
    if lawyer_id:
        appointments = appointments.filter(lawyer_id=lawyer_id)
    if case_id:
        appointments = appointments.filter(case_id=case_id)
    if date:
        appointments = appointments.filter(datetime__date=date)
    lawyers = User.objects.all()
    cases = Case.objects.all()
    # بيانات الرسم البياني (عدد المواعيد لكل محامي)
    chart_data = []
    for lawyer in lawyers:
        count = appointments.filter(lawyer=lawyer).count()
        chart_data.append({'label': str(lawyer), 'count': count})
    return render(request, 'reports/appointments_report.html', {
        'appointments': appointments,
        'lawyers': lawyers,
        'cases': cases,
        'selected_lawyer': lawyer_id,
        'selected_case': case_id,
        'selected_date': date,
        'chart_data': chart_data,
    })

def appointments_report_pdf(request):
    lawyer_id = request.GET.get('lawyer')
    case_id = request.GET.get('case')
    date = request.GET.get('date')
    appointments = Appointment.objects.all()
    if lawyer_id:
        appointments = appointments.filter(lawyer_id=lawyer_id)
    if case_id:
        appointments = appointments.filter(case_id=case_id)
    if date:
        appointments = appointments.filter(datetime__date=date)
    html = render_to_string('reports/appointments_report_pdf.html', {'appointments': appointments})
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename="appointments_report.pdf"'
    pisa.CreatePDF(io.BytesIO(html.encode('utf-8')), dest=response)
    return response

def appointments_report_excel(request):
    lawyer_id = request.GET.get('lawyer')
    case_id = request.GET.get('case')
    date = request.GET.get('date')
    appointments = Appointment.objects.all()
    if lawyer_id:
        appointments = appointments.filter(lawyer_id=lawyer_id)
    if case_id:
        appointments = appointments.filter(case_id=case_id)
    if date:
        appointments = appointments.filter(datetime__date=date)
    data = [
        [a.title, str(a.case), str(a.client), str(a.lawyer), a.datetime, a.location, a.notes]
        for a in appointments
    ]
    df = pd.DataFrame(data, columns=['العنوان', 'القضية', 'العميل', 'المحامي', 'التاريخ والوقت', 'الموقع', 'ملاحظات'])
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Appointments')
    output.seek(0)
    response = HttpResponse(output, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="appointments_report.xlsx"'
    return response

def documents_report(request):
    case_id = request.GET.get('case')
    client_id = request.GET.get('client')
    date = request.GET.get('date')
    documents = Document.objects.all()
    if case_id:
        documents = documents.filter(case_id=case_id)
    if client_id:
        documents = documents.filter(client_id=client_id)
    if date:
        documents = documents.filter(uploaded_at__date=date)
    cases = Case.objects.all()
    clients = Client.objects.all()
    # بيانات الرسم البياني (عدد المستندات لكل قضية)
    chart_data = []
    for case in cases:
        count = documents.filter(case=case).count()
        chart_data.append({'label': str(case), 'count': count})
    return render(request, 'reports/documents_report.html', {
        'documents': documents,
        'cases': cases,
        'clients': clients,
        'selected_case': case_id,
        'selected_client': client_id,
        'selected_date': date,
        'chart_data': chart_data,
    })

def documents_report_pdf(request):
    case_id = request.GET.get('case')
    client_id = request.GET.get('client')
    date = request.GET.get('date')
    documents = Document.objects.all()
    if case_id:
        documents = documents.filter(case_id=case_id)
    if client_id:
        documents = documents.filter(client_id=client_id)
    if date:
        documents = documents.filter(uploaded_at__date=date)
    html = render_to_string('reports/documents_report_pdf.html', {'documents': documents})
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename="documents_report.pdf"'
    pisa.CreatePDF(io.BytesIO(html.encode('utf-8')), dest=response)
    return response

def documents_report_excel(request):
    case_id = request.GET.get('case')
    client_id = request.GET.get('client')
    date = request.GET.get('date')
    documents = Document.objects.all()
    if case_id:
        documents = documents.filter(case_id=case_id)
    if client_id:
        documents = documents.filter(client_id=client_id)
    if date:
        documents = documents.filter(uploaded_at__date=date)
    data = [
        [d.name, str(d.case), str(d.client), d.uploaded_at, d.notes]
        for d in documents
    ]
    df = pd.DataFrame(data, columns=['اسم المستند', 'القضية', 'العميل', 'تاريخ الرفع', 'ملاحظات'])
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Documents')
    output.seek(0)
    response = HttpResponse(output, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="documents_report.xlsx"'
    return response

class ReportAccountsSummaryView(TemplateView):
    template_name = 'reports/accounts_summary.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        clients = Client.objects.all()
        clients_data = []
        total_paid = 0
        total_unpaid = 0
        for client in clients:
            cases = client.cases.all()
            total = 0
            for case in cases:
                fees = case.fees.all()
                total_case = sum(f.amount for f in fees)
                total += total_case
            paid = client.payments.aggregate(total_paid=Sum('amount'))['total_paid'] or 0
            unpaid = total - paid
            clients_data.append({
                'client': client,
                'paid': paid,
                'unpaid': unpaid,
                'total': total,
            })
            total_paid += paid
            total_unpaid += unpaid
        context['clients_data'] = clients_data
        context['total_paid'] = total_paid
        context['total_unpaid'] = total_unpaid
        context['total_fees'] = total_paid + total_unpaid
        return context

def accounts_summary_excel(request):
    from clients.models import Client
    from django.db.models import Sum
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = 'تقرير الحسابات'
    ws.append(['اسم العميل', 'إجمالي الأتعاب', 'المدفوع', 'المتبقي'])
    clients = Client.objects.all()
    for client in clients:
        cases = client.cases.all()
        total = 0
        for case in cases:
            fees = case.fees.all()
            total_case = sum(f.amount for f in fees)
            total += total_case
        paid = client.payments.aggregate(total_paid=Sum('amount'))['total_paid'] or 0
        unpaid = total - paid
        ws.append([client.full_name, float(total), float(paid), float(unpaid)])
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename=accounts_summary.xlsx'
    wb.save(response)
    return response

def accounts_summary_pdf(request):
    from clients.models import Client
    from django.db.models import Sum
    clients = Client.objects.all()
    clients_data = []
    total_paid = 0
    total_unpaid = 0
    for client in clients:
        cases = client.cases.all()
        total = 0
        for case in cases:
            fees = case.fees.all()
            total_case = sum(f.amount for f in fees)
            total += total_case
        paid = client.payments.aggregate(total_paid=Sum('amount'))['total_paid'] or 0
        unpaid = total - paid
        clients_data.append({
            'client': client,
            'paid': paid,
            'unpaid': unpaid,
            'total': total,
        })
        total_paid += paid
        total_unpaid += unpaid
    context = {
        'clients_data': clients_data,
        'total_paid': total_paid,
        'total_unpaid': total_unpaid,
        'total_fees': total_paid + total_unpaid,
    }
    html_string = render_to_string('reports/accounts_summary_pdf.html', context)
    pdf_file = HTML(string=html_string).write_pdf()
    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename=accounts_summary.pdf'
    return response
