from django.shortcuts import render, get_object_or_404, redirect
from django.urls import reverse_lazy
from django.views.generic import ListView, CreateView, UpdateView, DetailView, DeleteView
from .models import Client, Payment
from django.contrib.auth.mixins import PermissionRequiredMixin
from activitylog.utils import log_activity
from rest_framework import viewsets, permissions
from .serializers import ClientSerializer
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from cases.models import Fee, Case
from django.db.models import Sum
from .forms import PaymentForm
from django.contrib import messages
from django.db import models

# Create your views here.

class ClientListView(ListView):
    model = Client
    template_name = 'clients/client_list.html'
    context_object_name = 'clients'

class ClientCreateView(PermissionRequiredMixin, CreateView):
    permission_required = 'clients.add_client'
    model = Client
    fields = ['full_name', 'national_id', 'phone', 'email', 'address', 'notes']
    template_name = 'clients/client_form.html'
    success_url = reverse_lazy('clients:list')

    def form_valid(self, form):
        response = super().form_valid(form)
        client = self.object
        log_activity(self.request.user, 'client', client.pk, 'create', f'إضافة عميل: {client.full_name}')
        return response

class ClientUpdateView(PermissionRequiredMixin, UpdateView):
    permission_required = 'clients.change_client'
    model = Client
    fields = ['full_name', 'national_id', 'phone', 'email', 'address', 'notes']
    template_name = 'clients/client_form.html'
    success_url = reverse_lazy('clients:list')

    def form_valid(self, form):
        response = super().form_valid(form)
        client = self.object
        log_activity(self.request.user, 'client', client.pk, 'update', f'تعديل عميل: {client.full_name}')
        return response

class ClientDetailView(DetailView):
    model = Client
    template_name = 'clients/client_detail.html'
    context_object_name = 'client'

class ClientDeleteView(PermissionRequiredMixin, DeleteView):
    permission_required = 'clients.delete_client'
    model = Client
    template_name = 'clients/client_confirm_delete.html'
    success_url = reverse_lazy('clients:list')

    def delete(self, request, *args, **kwargs):
        client = self.get_object()
        log_activity(request.user, 'client', client.pk, 'delete', f'حذف عميل: {client.full_name}')
        return super().delete(request, *args, **kwargs)

class IsClientSelfOrAdmin(permissions.BasePermission):
    def has_object_permission(self, request, view, obj):
        if request.user.is_superuser or request.user.groups.filter(name='Admin').exists():
            return True
        if obj.user == request.user:
            return True
        return False

class ClientViewSet(viewsets.ModelViewSet):
    queryset = Client.objects.all()
    serializer_class = ClientSerializer
    permission_classes = [permissions.IsAuthenticated, IsClientSelfOrAdmin]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['national_id', 'phone', 'email']
    search_fields = ['full_name', 'national_id', 'phone', 'email']
    ordering_fields = ['full_name', 'national_id', 'phone', 'email']

class ClientAccountsReportView(DetailView):
    model = Client
    template_name = 'clients/client_accounts_report.html'
    context_object_name = 'client'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        cases = self.object.cases.all()
        case_fees = []
        total_paid = 0
        total_unpaid = 0
        for case in cases:
            fees = case.fees.all()
            paid = sum(f.amount for f in fees if f.is_paid)
            total = sum(f.amount for f in fees)
            unpaid = total - paid
            total_paid += paid
            total_unpaid += unpaid
            case_fees.append({
                'case': case,
                'fees': fees,
                'paid': paid,
                'unpaid': unpaid,
                'total': total,
            })
        context['case_fees'] = case_fees
        context['total_paid'] = total_paid
        context['total_unpaid'] = total_unpaid
        context['total_fees'] = total_paid + total_unpaid
        return context

def client_payments(request, client_id):
    client = get_object_or_404(Client, pk=client_id)
    payments = client.payments.select_related('case').all()
    total_payments = payments.aggregate(total=models.Sum('amount'))['total'] or 0
    count_payments = payments.count()
    last_payment = payments.order_by('-date').first()
    return render(request, 'clients/client_payments.html', {
        'client': client,
        'payments': payments,
        'total_payments': total_payments,
        'count_payments': count_payments,
        'last_payment': last_payment,
    })

def add_payment(request, client_id):
    client = get_object_or_404(Client, pk=client_id)
    if request.method == 'POST':
        form = PaymentForm(request.POST)
        if form.is_valid():
            payment = form.save(commit=False)
            payment.client = client
            form.save()
            messages.success(request, 'تمت إضافة الدفعة بنجاح.')
            return redirect('clients:client_payments', client_id=client.id)
    else:
        form = PaymentForm(initial={'client': client})
    return render(request, 'clients/add_payment.html', {'form': form, 'client': client})

def edit_payment(request, payment_id):
    payment = get_object_or_404(Payment, pk=payment_id)
    client = payment.client
    if request.method == 'POST':
        form = PaymentForm(request.POST, instance=payment)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تعديل الدفعة بنجاح.')
            return redirect('clients:client_payments', client_id=client.id)
    else:
        form = PaymentForm(instance=payment)
    return render(request, 'clients/edit_payment.html', {'form': form, 'client': client, 'payment': payment})

def delete_payment(request, payment_id):
    payment = get_object_or_404(Payment, pk=payment_id)
    client = payment.client
    if request.method == 'POST':
        payment.delete()
        messages.success(request, 'تم حذف الدفعة بنجاح.')
        return redirect('clients:client_payments', client_id=client.id)
    return render(request, 'clients/delete_payment_confirm.html', {'payment': payment, 'client': client})

def payment_detail(request, payment_id):
    payment = get_object_or_404(Payment, pk=payment_id)
    client = payment.client
    return render(request, 'clients/payment_detail.html', {'payment': payment, 'client': client})
