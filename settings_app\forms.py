from django import forms
from django.contrib.auth.forms import PasswordChangeForm
from django.contrib.auth import get_user_model
from .models import OfficeSettings

User = get_user_model()

class OfficeSettingsForm(forms.ModelForm):
    """Form for office settings configuration"""

    class Meta:
        model = OfficeSettings
        fields = [
            'office_name', 'office_address', 'office_phone', 'office_email',
            'office_website', 'office_logo', 'default_currency', 'timezone',
            'date_format', 'time_format', 'language', 'email_notifications',
            'sms_notifications', 'backup_frequency', 'max_file_size'
        ]
        widgets = {
            'office_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم مكتب المحاماة'
            }),
            'office_address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'عنوان المكتب'
            }),
            'office_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم هاتف المكتب'
            }),
            'office_email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني للمكتب'
            }),
            'office_website': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'موقع المكتب الإلكتروني (اختياري)'
            }),
            'office_logo': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'default_currency': forms.Select(attrs={
                'class': 'form-select'
            }),
            'timezone': forms.Select(attrs={
                'class': 'form-select'
            }),
            'date_format': forms.Select(attrs={
                'class': 'form-select'
            }),
            'time_format': forms.Select(attrs={
                'class': 'form-select'
            }),
            'language': forms.Select(attrs={
                'class': 'form-select'
            }),
            'email_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'sms_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'backup_frequency': forms.Select(attrs={
                'class': 'form-select'
            }),
            'max_file_size': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '100',
                'step': '1'
            })
        }
        labels = {
            'office_name': 'اسم المكتب',
            'office_address': 'عنوان المكتب',
            'office_phone': 'رقم الهاتف',
            'office_email': 'البريد الإلكتروني',
            'office_website': 'الموقع الإلكتروني',
            'office_logo': 'شعار المكتب',
            'default_currency': 'العملة الافتراضية',
            'timezone': 'المنطقة الزمنية',
            'date_format': 'تنسيق التاريخ',
            'time_format': 'تنسيق الوقت',
            'language': 'اللغة',
            'email_notifications': 'الإشعارات عبر البريد الإلكتروني',
            'sms_notifications': 'الإشعارات عبر الرسائل النصية',
            'backup_frequency': 'تكرار النسخ الاحتياطي',
            'max_file_size': 'الحد الأقصى لحجم الملف (MB)'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make some fields optional
        self.fields['office_website'].required = False
        self.fields['office_logo'].required = False


class UserProfileForm(forms.ModelForm):
    """Form for user profile editing"""

    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email']
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الأول'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الأخير'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني'
            })
        }
        labels = {
            'first_name': 'الاسم الأول',
            'last_name': 'الاسم الأخير',
            'email': 'البريد الإلكتروني'
        }

    def clean_email(self):
        email = self.cleaned_data['email']
        # Check if email already exists (excluding current user)
        existing_user = User.objects.filter(email=email)
        if self.instance.pk:
            existing_user = existing_user.exclude(pk=self.instance.pk)

        if existing_user.exists():
            raise forms.ValidationError('البريد الإلكتروني مستخدم مسبقاً')

        return email


class CustomPasswordChangeForm(PasswordChangeForm):
    """Custom password change form with Bootstrap styling"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.fields['old_password'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'كلمة المرور الحالية'
        })
        self.fields['new_password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'كلمة المرور الجديدة'
        })
        self.fields['new_password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'تأكيد كلمة المرور الجديدة'
        })

        self.fields['old_password'].label = 'كلمة المرور الحالية'
        self.fields['new_password1'].label = 'كلمة المرور الجديدة'
        self.fields['new_password2'].label = 'تأكيد كلمة المرور الجديدة'


class NotificationPreferencesForm(forms.Form):
    """Form for notification preferences"""

    email_case_updates = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='تحديثات القضايا عبر البريد الإلكتروني'
    )

    email_appointment_reminders = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='تذكيرات المواعيد عبر البريد الإلكتروني'
    )

    email_document_uploads = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='إشعارات رفع المستندات عبر البريد الإلكتروني'
    )

    sms_urgent_notifications = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='الإشعارات العاجلة عبر الرسائل النصية'
    )

    sms_appointment_reminders = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='تذكيرات المواعيد عبر الرسائل النصية'
    )

    notification_frequency = forms.ChoiceField(
        choices=[
            ('immediate', 'فوري'),
            ('daily', 'يومي'),
            ('weekly', 'أسبوعي'),
            ('monthly', 'شهري')
        ],
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='تكرار الإشعارات'
    )


class SystemMaintenanceForm(forms.Form):
    """Form for system maintenance operations"""

    backup_database = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='نسخ احتياطي لقاعدة البيانات'
    )

    backup_files = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='نسخ احتياطي للملفات'
    )

    clean_temp_files = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='تنظيف الملفات المؤقتة'
    )

    optimize_database = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='تحسين قاعدة البيانات'
    )

    maintenance_message = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'رسالة الصيانة للمستخدمين (اختياري)'
        }),
        label='رسالة الصيانة'
    )

    def clean(self):
        cleaned_data = super().clean()
        backup_database = cleaned_data.get('backup_database')
        backup_files = cleaned_data.get('backup_files')
        clean_temp_files = cleaned_data.get('clean_temp_files')
        optimize_database = cleaned_data.get('optimize_database')

        if not any([backup_database, backup_files, clean_temp_files, optimize_database]):
            raise forms.ValidationError('يجب اختيار عملية صيانة واحدة على الأقل')

        return cleaned_data