from django.shortcuts import render
from cases.models import Case
from clients.models import Client
from appointments.models import Appointment
from documents.models import Document
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from datetime import timedelta

@login_required
def dashboard_view(request):
    today = timezone.now().date()
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)
    cases_open = Case.objects.filter(status='open').count()
    cases_closed = Case.objects.filter(status='closed').count()
    cases_pending = Case.objects.filter(status='pending').count()
    appointments_today = Appointment.objects.filter(datetime__date=today).count()
    appointments_week = Appointment.objects.filter(datetime__date__range=[week_start, week_end]).count()
    documents_month = Document.objects.filter(uploaded_at__month=today.month, uploaded_at__year=today.year).count()
    last_cases = Case.objects.order_by('-id')[:5]
    last_appointments = Appointment.objects.order_by('-datetime')[:5]
    context = {
        'cases_count': Case.objects.count(),
        'clients_count': Client.objects.count(),
        'appointments_count': Appointment.objects.count(),
        'documents_count': Document.objects.count(),
        'cases_open': cases_open,
        'cases_closed': cases_closed,
        'cases_pending': cases_pending,
        'appointments_today': appointments_today,
        'appointments_week': appointments_week,
        'documents_month': documents_month,
        'last_cases': last_cases,
        'last_appointments': last_appointments,
    }
    return render(request, 'dashboard/dashboard.html', context)
