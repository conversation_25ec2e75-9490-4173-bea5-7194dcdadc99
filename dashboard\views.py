from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.db.models import Count, Q, Sum
from django.http import JsonResponse
from datetime import timedelta, datetime
from cases.models import Case, Fee
from clients.models import Client, Payment
from appointments.models import Appointment
from documents.models import Document
from notifications.models import Notification
from users.models import User
import json

@login_required
def dashboard_view(request):
    """Enhanced dashboard view with comprehensive analytics"""
    today = timezone.now().date()
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)
    month_start = today.replace(day=1)

    # Basic counts
    cases_count = Case.objects.count()
    clients_count = Client.objects.count()
    appointments_count = Appointment.objects.count()
    documents_count = Document.objects.count()

    # Case statistics
    cases_open = Case.objects.filter(status='open').count()
    cases_closed = Case.objects.filter(status='closed').count()
    cases_pending = Case.objects.filter(status='pending').count()

    # Appointment statistics
    appointments_today = Appointment.objects.filter(datetime__date=today).count()
    appointments_week = Appointment.objects.filter(datetime__date__range=[week_start, week_end]).count()
    appointments_upcoming = Appointment.objects.filter(
        datetime__gte=timezone.now(),
        datetime__lte=timezone.now() + timedelta(days=7)
    ).count()

    # Document statistics
    documents_month = Document.objects.filter(
        uploaded_at__month=today.month,
        uploaded_at__year=today.year
    ).count()
    documents_week = Document.objects.filter(
        uploaded_at__date__range=[week_start, week_end]
    ).count()

    # Recent items
    last_cases = Case.objects.select_related('client', 'lawyer').order_by('-id')[:5]
    last_appointments = Appointment.objects.select_related('client', 'case', 'lawyer').order_by('-datetime')[:5]
    last_documents = Document.objects.select_related('case', 'client').order_by('-uploaded_at')[:5]

    # Financial statistics
    total_payments = Payment.objects.aggregate(total=Sum('amount'))['total'] or 0
    payments_this_month = Payment.objects.filter(
        date__gte=month_start
    ).aggregate(total=Sum('amount'))['total'] or 0

    # User activity
    user_cases = Case.objects.filter(lawyer=request.user).count() if request.user.is_staff else 0
    user_appointments = Appointment.objects.filter(lawyer=request.user).count() if request.user.is_staff else 0

    # Notifications
    unread_notifications = Notification.objects.filter(
        user=request.user,
        is_read=False
    ).count()

    # Monthly case trends (last 6 months)
    monthly_cases = []
    for i in range(6):
        month_date = today.replace(day=1) - timedelta(days=30*i)
        month_cases = Case.objects.filter(
            start_date__year=month_date.year,
            start_date__month=month_date.month
        ).count()
        monthly_cases.append({
            'month': month_date.strftime('%B'),
            'count': month_cases
        })
    monthly_cases.reverse()

    # Case status distribution for charts
    case_status_data = [
        {'status': 'مفتوحة', 'count': cases_open, 'color': '#28a745'},
        {'status': 'مغلقة', 'count': cases_closed, 'color': '#6c757d'},
        {'status': 'معلقة', 'count': cases_pending, 'color': '#ffc107'},
    ]

    context = {
        # Basic counts
        'cases_count': cases_count,
        'clients_count': clients_count,
        'appointments_count': appointments_count,
        'documents_count': documents_count,

        # Case statistics
        'cases_open': cases_open,
        'cases_closed': cases_closed,
        'cases_pending': cases_pending,

        # Appointment statistics
        'appointments_today': appointments_today,
        'appointments_week': appointments_week,
        'appointments_upcoming': appointments_upcoming,

        # Document statistics
        'documents_month': documents_month,
        'documents_week': documents_week,

        # Recent items
        'last_cases': last_cases,
        'last_appointments': last_appointments,
        'last_documents': last_documents,

        # Financial data
        'total_payments': total_payments,
        'payments_this_month': payments_this_month,

        # User specific data
        'user_cases': user_cases,
        'user_appointments': user_appointments,
        'unread_notifications': unread_notifications,

        # Chart data
        'monthly_cases': json.dumps(monthly_cases),
        'case_status_data': json.dumps(case_status_data),

        # Date context
        'today': today,
        'week_start': week_start,
        'week_end': week_end,
    }

    return render(request, 'dashboard/dashboard.html', context)


@login_required
def dashboard_api_stats(request):
    """API endpoint for dashboard statistics"""
    today = timezone.now().date()

    # Get statistics based on the requested period
    period = request.GET.get('period', 'week')

    if period == 'week':
        start_date = today - timedelta(days=7)
    elif period == 'month':
        start_date = today - timedelta(days=30)
    elif period == 'year':
        start_date = today - timedelta(days=365)
    else:
        start_date = today - timedelta(days=7)

    stats = {
        'cases': Case.objects.filter(start_date__gte=start_date).count(),
        'clients': Client.objects.filter(id__in=Case.objects.filter(
            start_date__gte=start_date
        ).values_list('client_id', flat=True)).count(),
        'appointments': Appointment.objects.filter(datetime__date__gte=start_date).count(),
        'documents': Document.objects.filter(uploaded_at__date__gte=start_date).count(),
    }

    return JsonResponse(stats)


@login_required
def dashboard_chart_data(request):
    """API endpoint for chart data"""
    chart_type = request.GET.get('type', 'cases_monthly')

    if chart_type == 'cases_monthly':
        # Monthly case data for the last 12 months
        data = []
        today = timezone.now().date()

        for i in range(12):
            month_date = today.replace(day=1) - timedelta(days=30*i)
            month_cases = Case.objects.filter(
                start_date__year=month_date.year,
                start_date__month=month_date.month
            ).count()
            data.append({
                'month': month_date.strftime('%Y-%m'),
                'month_name': month_date.strftime('%B %Y'),
                'count': month_cases
            })

        data.reverse()
        return JsonResponse({'data': data})

    elif chart_type == 'case_status':
        # Case status distribution
        data = [
            {
                'status': 'مفتوحة',
                'count': Case.objects.filter(status='open').count(),
                'color': '#28a745'
            },
            {
                'status': 'مغلقة',
                'count': Case.objects.filter(status='closed').count(),
                'color': '#6c757d'
            },
            {
                'status': 'معلقة',
                'count': Case.objects.filter(status='pending').count(),
                'color': '#ffc107'
            }
        ]
        return JsonResponse({'data': data})

    elif chart_type == 'appointments_weekly':
        # Weekly appointment data
        data = []
        today = timezone.now().date()

        for i in range(7):
            day = today - timedelta(days=i)
            day_appointments = Appointment.objects.filter(datetime__date=day).count()
            data.append({
                'day': day.strftime('%Y-%m-%d'),
                'day_name': day.strftime('%A'),
                'count': day_appointments
            })

        data.reverse()
        return JsonResponse({'data': data})

    return JsonResponse({'error': 'Invalid chart type'}, status=400)

@login_required
def financial_summary_api(request):
    """API endpoint for financial summary data"""
    today = timezone.now().date()
    month_start = today.replace(day=1)
    year_start = today.replace(month=1, day=1)

    # Monthly financial data
    monthly_payments = Payment.objects.filter(
        date__gte=month_start
    ).aggregate(total=Sum('amount'))['total'] or 0

    monthly_fees = Fee.objects.filter(
        due_date__gte=month_start,
        status='pending'
    ).aggregate(total=Sum('amount'))['total'] or 0

    # Yearly financial data
    yearly_payments = Payment.objects.filter(
        date__gte=year_start
    ).aggregate(total=Sum('amount'))['total'] or 0

    yearly_fees = Fee.objects.filter(
        due_date__gte=year_start
    ).aggregate(total=Sum('amount'))['total'] or 0

    # Outstanding fees
    outstanding_fees = Fee.objects.filter(
        status='pending',
        due_date__lt=today
    ).aggregate(total=Sum('amount'))['total'] or 0

    return JsonResponse({
        'monthly': {
            'payments': float(monthly_payments),
            'pending_fees': float(monthly_fees)
        },
        'yearly': {
            'payments': float(yearly_payments),
            'total_fees': float(yearly_fees)
        },
        'outstanding_fees': float(outstanding_fees)
    })

@login_required
def performance_metrics_api(request):
    """API endpoint for performance metrics"""
    today = timezone.now().date()
    month_start = today.replace(day=1)

    # Case resolution metrics
    cases_opened_month = Case.objects.filter(start_date__gte=month_start).count()
    cases_closed_month = Case.objects.filter(
        status='closed',
        end_date__gte=month_start
    ).count()

    # Average case duration
    closed_cases = Case.objects.filter(
        status='closed',
        end_date__isnull=False
    )

    avg_duration = 0
    if closed_cases.exists():
        total_days = sum([
            (case.end_date - case.start_date).days
            for case in closed_cases
            if case.end_date and case.start_date
        ])
        avg_duration = total_days / closed_cases.count() if closed_cases.count() > 0 else 0

    # Client satisfaction (placeholder - would need actual feedback system)
    client_satisfaction = 85  # Placeholder percentage

    # Appointment efficiency
    total_appointments = Appointment.objects.filter(datetime__date__gte=month_start).count()
    completed_appointments = Appointment.objects.filter(
        datetime__date__gte=month_start,
        status='completed'
    ).count()

    appointment_efficiency = (completed_appointments / total_appointments * 100) if total_appointments > 0 else 0

    return JsonResponse({
        'cases': {
            'opened_this_month': cases_opened_month,
            'closed_this_month': cases_closed_month,
            'average_duration_days': round(avg_duration, 1)
        },
        'client_satisfaction': client_satisfaction,
        'appointment_efficiency': round(appointment_efficiency, 1)
    })
