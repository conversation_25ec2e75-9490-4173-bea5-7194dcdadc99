from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.db.models import Count, Q, Sum
from django.http import JsonResponse
from datetime import timedelta, datetime
from cases.models import Case
from clients.models import Client, Payment
from appointments.models import Appointment
from documents.models import Document
from notifications.models import Notification
import json

@login_required
def dashboard_view(request):
    """Enhanced dashboard view with comprehensive analytics"""
    today = timezone.now().date()
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)
    month_start = today.replace(day=1)

    # Basic counts
    cases_count = Case.objects.count()
    clients_count = Client.objects.count()
    appointments_count = Appointment.objects.count()
    documents_count = Document.objects.count()

    # Case statistics
    cases_open = Case.objects.filter(status='open').count()
    cases_closed = Case.objects.filter(status='closed').count()
    cases_pending = Case.objects.filter(status='pending').count()

    # Appointment statistics
    appointments_today = Appointment.objects.filter(datetime__date=today).count()
    appointments_week = Appointment.objects.filter(datetime__date__range=[week_start, week_end]).count()
    appointments_upcoming = Appointment.objects.filter(
        datetime__gte=timezone.now(),
        datetime__lte=timezone.now() + timedelta(days=7)
    ).count()

    # Document statistics
    documents_month = Document.objects.filter(
        uploaded_at__month=today.month,
        uploaded_at__year=today.year
    ).count()
    documents_week = Document.objects.filter(
        uploaded_at__date__range=[week_start, week_end]
    ).count()

    # Recent items
    last_cases = Case.objects.select_related('client', 'lawyer').order_by('-id')[:5]
    last_appointments = Appointment.objects.select_related('client', 'case', 'lawyer').order_by('-datetime')[:5]
    last_documents = Document.objects.select_related('case', 'client').order_by('-uploaded_at')[:5]

    # Financial statistics
    total_payments = Payment.objects.aggregate(total=Sum('amount'))['total'] or 0
    payments_this_month = Payment.objects.filter(
        date__gte=month_start
    ).aggregate(total=Sum('amount'))['total'] or 0

    # User activity
    user_cases = Case.objects.filter(lawyer=request.user).count() if request.user.is_staff else 0
    user_appointments = Appointment.objects.filter(lawyer=request.user).count() if request.user.is_staff else 0

    # Notifications
    unread_notifications = Notification.objects.filter(
        user=request.user,
        is_read=False
    ).count()

    # Monthly case trends (last 6 months)
    monthly_cases = []
    for i in range(6):
        month_date = today.replace(day=1) - timedelta(days=30*i)
        month_cases = Case.objects.filter(
            start_date__year=month_date.year,
            start_date__month=month_date.month
        ).count()
        monthly_cases.append({
            'month': month_date.strftime('%B'),
            'count': month_cases
        })
    monthly_cases.reverse()

    # Case status distribution for charts
    case_status_data = [
        {'status': 'مفتوحة', 'count': cases_open, 'color': '#28a745'},
        {'status': 'مغلقة', 'count': cases_closed, 'color': '#6c757d'},
        {'status': 'معلقة', 'count': cases_pending, 'color': '#ffc107'},
    ]

    context = {
        # Basic counts
        'cases_count': cases_count,
        'clients_count': clients_count,
        'appointments_count': appointments_count,
        'documents_count': documents_count,

        # Case statistics
        'cases_open': cases_open,
        'cases_closed': cases_closed,
        'cases_pending': cases_pending,

        # Appointment statistics
        'appointments_today': appointments_today,
        'appointments_week': appointments_week,
        'appointments_upcoming': appointments_upcoming,

        # Document statistics
        'documents_month': documents_month,
        'documents_week': documents_week,

        # Recent items
        'last_cases': last_cases,
        'last_appointments': last_appointments,
        'last_documents': last_documents,

        # Financial data
        'total_payments': total_payments,
        'payments_this_month': payments_this_month,

        # User specific data
        'user_cases': user_cases,
        'user_appointments': user_appointments,
        'unread_notifications': unread_notifications,

        # Chart data
        'monthly_cases': json.dumps(monthly_cases),
        'case_status_data': json.dumps(case_status_data),

        # Date context
        'today': today,
        'week_start': week_start,
        'week_end': week_end,
    }

    return render(request, 'dashboard/dashboard.html', context)


@login_required
def dashboard_api_stats(request):
    """API endpoint for dashboard statistics"""
    today = timezone.now().date()

    # Get statistics based on the requested period
    period = request.GET.get('period', 'week')

    if period == 'week':
        start_date = today - timedelta(days=7)
    elif period == 'month':
        start_date = today - timedelta(days=30)
    elif period == 'year':
        start_date = today - timedelta(days=365)
    else:
        start_date = today - timedelta(days=7)

    stats = {
        'cases': Case.objects.filter(start_date__gte=start_date).count(),
        'clients': Client.objects.filter(id__in=Case.objects.filter(
            start_date__gte=start_date
        ).values_list('client_id', flat=True)).count(),
        'appointments': Appointment.objects.filter(datetime__date__gte=start_date).count(),
        'documents': Document.objects.filter(uploaded_at__date__gte=start_date).count(),
    }

    return JsonResponse(stats)


@login_required
def dashboard_chart_data(request):
    """API endpoint for chart data"""
    chart_type = request.GET.get('type', 'cases_monthly')

    if chart_type == 'cases_monthly':
        # Monthly case data for the last 12 months
        data = []
        today = timezone.now().date()

        for i in range(12):
            month_date = today.replace(day=1) - timedelta(days=30*i)
            month_cases = Case.objects.filter(
                start_date__year=month_date.year,
                start_date__month=month_date.month
            ).count()
            data.append({
                'month': month_date.strftime('%Y-%m'),
                'month_name': month_date.strftime('%B %Y'),
                'count': month_cases
            })

        data.reverse()
        return JsonResponse({'data': data})

    elif chart_type == 'case_status':
        # Case status distribution
        data = [
            {
                'status': 'مفتوحة',
                'count': Case.objects.filter(status='open').count(),
                'color': '#28a745'
            },
            {
                'status': 'مغلقة',
                'count': Case.objects.filter(status='closed').count(),
                'color': '#6c757d'
            },
            {
                'status': 'معلقة',
                'count': Case.objects.filter(status='pending').count(),
                'color': '#ffc107'
            }
        ]
        return JsonResponse({'data': data})

    elif chart_type == 'appointments_weekly':
        # Weekly appointment data
        data = []
        today = timezone.now().date()

        for i in range(7):
            day = today - timedelta(days=i)
            day_appointments = Appointment.objects.filter(datetime__date=day).count()
            data.append({
                'day': day.strftime('%Y-%m-%d'),
                'day_name': day.strftime('%A'),
                'count': day_appointments
            })

        data.reverse()
        return JsonResponse({'data': data})

    return JsonResponse({'error': 'Invalid chart type'}, status=400)
