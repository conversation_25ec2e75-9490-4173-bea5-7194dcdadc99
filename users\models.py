from django.contrib.auth.models import AbstractUser, Group, Permission
from django.db import models
from django.utils import timezone
from django.core.validators import RegexValidator
from PIL import Image
import os

class UserRole(models.Model):
    """User roles for the law office system"""

    ROLE_TYPES = [
        ('admin', 'مدير عام'),
        ('lawyer', 'محامي'),
        ('assistant', 'مساعد قانوني'),
        ('secretary', 'سكرتير'),
        ('accountant', 'محاسب'),
        ('client', 'عميل'),
    ]

    name = models.CharField(max_length=50, choices=ROLE_TYPES, unique=True, verbose_name='اسم الدور')
    display_name = models.CharField(max_length=100, verbose_name='الاسم المعروض')
    description = models.TextField(blank=True, verbose_name='الوصف')
    permissions = models.ManyToManyField(Permission, blank=True, verbose_name='الصلاحيات')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'دور المستخدم'
        verbose_name_plural = 'أدوار المستخدمين'
        ordering = ['name']

    def __str__(self):
        return self.display_name

class User(AbstractUser):
    """Enhanced User model with additional fields for law office management"""

    GENDER_CHOICES = [
        ('M', 'ذكر'),
        ('F', 'أنثى'),
    ]

    LANGUAGE_CHOICES = [
        ('ar', 'العربية'),
        ('en', 'English'),
    ]

    # Personal Information
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح."
    )
    phone = models.CharField(
        validators=[phone_regex],
        max_length=17,
        blank=True,
        verbose_name='رقم الهاتف'
    )
    mobile = models.CharField(
        validators=[phone_regex],
        max_length=17,
        blank=True,
        verbose_name='رقم الجوال'
    )

    national_id = models.CharField(
        max_length=20,
        blank=True,
        unique=True,
        null=True,
        verbose_name='رقم الهوية'
    )

    date_of_birth = models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, blank=True, verbose_name='الجنس')
    address = models.TextField(blank=True, verbose_name='العنوان')

    # Professional Information
    role = models.ForeignKey(
        UserRole,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='الدور'
    )
    department = models.CharField(max_length=100, blank=True, verbose_name='القسم')
    position = models.CharField(max_length=100, blank=True, verbose_name='المنصب')
    employee_id = models.CharField(max_length=20, blank=True, unique=True, null=True, verbose_name='رقم الموظف')
    hire_date = models.DateField(blank=True, null=True, verbose_name='تاريخ التوظيف')
    salary = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name='الراتب')

    # Profile and Preferences
    profile_picture = models.ImageField(
        upload_to='profile_pictures/',
        blank=True,
        null=True,
        verbose_name='الصورة الشخصية'
    )
    bio = models.TextField(blank=True, verbose_name='نبذة شخصية')
    website = models.URLField(blank=True, verbose_name='الموقع الشخصي')

    # System Preferences
    language = models.CharField(
        max_length=5,
        choices=LANGUAGE_CHOICES,
        default='ar',
        verbose_name='اللغة المفضلة'
    )
    timezone = models.CharField(max_length=50, default='Asia/Riyadh', verbose_name='المنطقة الزمنية')
    theme = models.CharField(
        max_length=10,
        choices=[('light', 'فاتح'), ('dark', 'داكن')],
        default='light',
        verbose_name='المظهر'
    )

    # Notification Preferences
    email_notifications = models.BooleanField(default=True, verbose_name='إشعارات البريد الإلكتروني')
    sms_notifications = models.BooleanField(default=False, verbose_name='إشعارات الرسائل النصية')
    push_notifications = models.BooleanField(default=True, verbose_name='الإشعارات المنبثقة')

    # Security and Access
    two_factor_enabled = models.BooleanField(default=False, verbose_name='المصادقة الثنائية')
    last_password_change = models.DateTimeField(blank=True, null=True, verbose_name='آخر تغيير كلمة مرور')
    failed_login_attempts = models.PositiveIntegerField(default=0, verbose_name='محاولات تسجيل الدخول الفاشلة')
    account_locked_until = models.DateTimeField(blank=True, null=True, verbose_name='الحساب مقفل حتى')

    # Activity Tracking
    last_activity = models.DateTimeField(blank=True, null=True, verbose_name='آخر نشاط')
    login_count = models.PositiveIntegerField(default=0, verbose_name='عدد مرات تسجيل الدخول')

    # Additional Fields
    notes = models.TextField(blank=True, verbose_name='ملاحظات إدارية')
    is_verified = models.BooleanField(default=False, verbose_name='محقق')
    verification_date = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التحقق')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'مستخدم'
        verbose_name_plural = 'المستخدمون'
        ordering = ['-date_joined']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['national_id']),
            models.Index(fields=['employee_id']),
            models.Index(fields=['role']),
        ]

    def __str__(self):
        return self.get_full_name() or self.username

    def save(self, *args, **kwargs):
        # Resize profile picture if it's too large
        super().save(*args, **kwargs)

        if self.profile_picture:
            img = Image.open(self.profile_picture.path)
            if img.height > 300 or img.width > 300:
                output_size = (300, 300)
                img.thumbnail(output_size)
                img.save(self.profile_picture.path)

    @property
    def full_name(self):
        """Get user's full name"""
        return self.get_full_name() or self.username

    @property
    def age(self):
        """Calculate user's age"""
        if self.date_of_birth:
            today = timezone.now().date()
            return today.year - self.date_of_birth.year - (
                (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
            )
        return None

    @property
    def years_of_service(self):
        """Calculate years of service"""
        if self.hire_date:
            today = timezone.now().date()
            return today.year - self.hire_date.year - (
                (today.month, today.day) < (self.hire_date.month, self.hire_date.day)
            )
        return None

    @property
    def is_account_locked(self):
        """Check if account is locked"""
        if self.account_locked_until:
            return timezone.now() < self.account_locked_until
        return False

    def lock_account(self, duration_minutes=30):
        """Lock user account for specified duration"""
        self.account_locked_until = timezone.now() + timezone.timedelta(minutes=duration_minutes)
        self.save(update_fields=['account_locked_until'])

    def unlock_account(self):
        """Unlock user account"""
        self.account_locked_until = None
        self.failed_login_attempts = 0
        self.save(update_fields=['account_locked_until', 'failed_login_attempts'])

    def record_login_attempt(self, success=True):
        """Record login attempt"""
        if success:
            self.failed_login_attempts = 0
            self.login_count += 1
            self.last_login = timezone.now()
            self.last_activity = timezone.now()
        else:
            self.failed_login_attempts += 1
            # Lock account after 5 failed attempts
            if self.failed_login_attempts >= 5:
                self.lock_account()

        self.save(update_fields=[
            'failed_login_attempts', 'login_count', 'last_login', 'last_activity'
        ])

    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = timezone.now()
        self.save(update_fields=['last_activity'])

    def has_role(self, role_name):
        """Check if user has specific role"""
        return self.role and self.role.name == role_name

    def get_permissions_list(self):
        """Get list of user permissions"""
        permissions = set()

        # Add user permissions
        permissions.update(self.user_permissions.values_list('codename', flat=True))

        # Add group permissions
        for group in self.groups.all():
            permissions.update(group.permissions.values_list('codename', flat=True))

        # Add role permissions
        if self.role:
            permissions.update(self.role.permissions.values_list('codename', flat=True))

        return list(permissions)

    def can_access_case(self, case):
        """Check if user can access specific case"""
        if self.is_superuser:
            return True

        if self.has_role('admin'):
            return True

        if self.has_role('lawyer') and case.lawyer == self:
            return True

        # Add more access control logic as needed
        return False

    def get_dashboard_stats(self):
        """Get user-specific dashboard statistics"""
        from cases.models import Case
        from appointments.models import Appointment
        from documents.models import Document

        stats = {}

        if self.has_role('lawyer'):
            stats['my_cases'] = Case.objects.filter(lawyer=self).count()
            stats['my_appointments'] = Appointment.objects.filter(lawyer=self).count()
            stats['my_documents'] = Document.objects.filter(uploaded_by=self).count()

        return stats
