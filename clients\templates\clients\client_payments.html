{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-md-9 col-lg-8">
            <div class="card shadow rounded-4 border-0 p-4">
                <h4 class="mb-3 fw-bold"><i class="bi bi-cash-stack text-success me-2"></i>دفعات العميل: {{ client.full_name }}</h4>
                <div class="d-flex justify-content-end mb-3">
                    <a href="{% url 'clients:add_payment' client.id %}" class="btn btn-success btn-sm px-4">
                        <i class="bi bi-plus-circle"></i> إضافة دفعة جديدة
                    </a>
                </div>
                <div class="mb-3">
                    <div class="alert alert-info">
                        <strong>إجمالي الدفعات:</strong> {{ total_payments|floatformat:2 }} ج.م |
                        <strong>عدد الدفعات:</strong> {{ count_payments }} |
                        <strong>آخر دفعة:</strong>
                        {% if last_payment %}
                            {{ last_payment.date }} ({{ last_payment.amount|floatformat:2 }} ج.م)
                        {% else %}
                            لا يوجد
                        {% endif %}
                    </div>
                </div>
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>القضية</th>
                            <th>ملاحظات</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                        <tr>
                            <td>{{ payment.date }}</td>
                            <td>{{ payment.amount|floatformat:2 }}</td>
                            <td>{% if payment.case %}{{ payment.case.name }}{% else %}-{% endif %}</td>
                            <td>{{ payment.notes|default:'-' }}</td>
                            <td>
                                <a href="{% url 'clients:payment_detail' payment.id %}" class="btn btn-outline-info btn-sm" title="تفاصيل"><i class="bi bi-eye"></i></a>
                                <a href="{% url 'clients:edit_payment' payment.id %}" class="btn btn-outline-primary btn-sm" title="تعديل"><i class="bi bi-pencil"></i></a>
                                <a href="{% url 'clients:delete_payment' payment.id %}" class="btn btn-outline-danger btn-sm" title="حذف"><i class="bi bi-trash"></i></a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4" class="text-center">لا توجد دفعات مسجلة لهذا العميل.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                <div class="d-flex justify-content-end">
                    <a href="{% url 'clients:detail' client.id %}" class="btn btn-link"><i class="bi bi-arrow-right-circle"></i> العودة لتفاصيل العميل</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 