from django import forms
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
from .models import Client, Payment
from cases.models import Case
import re

class ClientForm(forms.ModelForm):
    """Form for creating and editing clients"""

    class Meta:
        model = Client
        fields = ['full_name', 'national_id', 'phone', 'email', 'address', 'notes']
        widgets = {
            'full_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الكامل للعميل'
            }),
            'national_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهوية أو جواز السفر'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهاتف (مثال: 0501234567)'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني (اختياري)'
            }),
            'address': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'العنوان (اختياري)'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'ملاحظات إضافية حول العميل (اختياري)'
            })
        }
        labels = {
            'full_name': 'الاسم الكامل',
            'national_id': 'رقم الهوية/جواز السفر',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'address': 'العنوان',
            'notes': 'ملاحظات'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make optional fields
        self.fields['email'].required = False
        self.fields['address'].required = False
        self.fields['notes'].required = False

    def clean_phone(self):
        phone = self.cleaned_data['phone']
        # Remove spaces and special characters
        phone = re.sub(r'[^\d+]', '', phone)

        # Validate Saudi phone number format
        if not re.match(r'^(05|5)\d{8}$|^\+966(5)\d{8}$', phone):
            raise ValidationError('رقم الهاتف غير صحيح. يجب أن يكون بصيغة 05xxxxxxxx')

        return phone

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            # Check if email already exists (excluding current instance)
            existing_client = Client.objects.filter(email=email)
            if self.instance.pk:
                existing_client = existing_client.exclude(pk=self.instance.pk)

            if existing_client.exists():
                raise ValidationError('البريد الإلكتروني مستخدم مسبقاً')

        return email

    def clean_national_id(self):
        national_id = self.cleaned_data['national_id']

        # Check if national ID already exists (excluding current instance)
        existing_client = Client.objects.filter(national_id=national_id)
        if self.instance.pk:
            existing_client = existing_client.exclude(pk=self.instance.pk)

        if existing_client.exists():
            raise ValidationError('رقم الهوية مستخدم مسبقاً')

        return national_id


class PaymentForm(forms.ModelForm):
    """Form for recording client payments"""

    class Meta:
        model = Payment
        fields = ['case', 'amount', 'date', 'notes']
        widgets = {
            'case': forms.Select(attrs={
                'class': 'form-select'
            }),
            'amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': 'المبلغ المدفوع'
            }),
            'date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'notes': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'ملاحظات اختيارية حول الدفعة'
            })
        }
        labels = {
            'case': 'القضية',
            'amount': 'المبلغ',
            'date': 'تاريخ الدفعة',
            'notes': 'ملاحظات'
        }

    def __init__(self, *args, **kwargs):
        client = kwargs.pop('client', None)
        super().__init__(*args, **kwargs)

        # Filter cases for the specific client
        if client:
            self.fields['case'].queryset = Case.objects.filter(client=client)

        self.fields['notes'].required = False

    def clean_amount(self):
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise ValidationError('المبلغ يجب أن يكون أكبر من صفر')
        return amount


class ClientSearchForm(forms.Form):
    """Form for searching clients"""

    search_query = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'ابحث في الاسم، رقم الهوية، أو رقم الهاتف...'
        }),
        label='البحث'
    )

    has_email = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='لديه بريد إلكتروني'
    )

    has_cases = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='لديه قضايا'
    )

    registration_date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='مسجل من تاريخ'
    )

    registration_date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='مسجل إلى تاريخ'
    )

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('registration_date_from')
        date_to = cleaned_data.get('registration_date_to')

        if date_from and date_to and date_to < date_from:
            raise ValidationError('تاريخ النهاية يجب أن يكون بعد تاريخ البداية')

        return cleaned_data


class ClientContactForm(forms.Form):
    """Form for contacting clients"""

    subject = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'موضوع الرسالة'
        }),
        label='الموضوع'
    )

    message = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 6,
            'placeholder': 'نص الرسالة'
        }),
        label='الرسالة'
    )

    send_email = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='إرسال عبر البريد الإلكتروني'
    )

    send_sms = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='إرسال عبر الرسائل النصية'
    )

    def clean(self):
        cleaned_data = super().clean()
        send_email = cleaned_data.get('send_email')
        send_sms = cleaned_data.get('send_sms')

        if not send_email and not send_sms:
            raise ValidationError('يجب اختيار طريقة إرسال واحدة على الأقل')

        return cleaned_data