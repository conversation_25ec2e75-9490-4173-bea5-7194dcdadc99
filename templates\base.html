{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="نظام إدارة مكتب المحاماة - إدارة شاملة للقضايا والعملاء والمواعيد">
    <meta name="keywords" content="مكتب محاماة, إدارة قضايا, نظام قانوني, محامين">
    <meta name="author" content="Lawyers Office System">

    <title>{% block title %}نظام مكتب المحاماة{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Animate.css for animations -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/custom.css' %}" rel="stylesheet">

    <!-- Additional CSS for specific pages -->
    {% block extra_css %}{% endblock %}

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#222b45">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="مكتب المحاماة">
</head>
<body data-theme="light" class="{% if user.is_authenticated %}authenticated{% else %}guest{% endif %}">
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" style="background: rgba(255,255,255,0.9); z-index: 9999;">
        <div class="d-flex justify-content-center align-items-center h-100">
            <div class="text-center">
                <div class="loading-spinner mb-3" style="width: 3rem; height: 3rem;"></div>
                <p class="text-muted">جاري التحميل...</p>
            </div>
        </div>
    </div>

    <!-- Mobile Overlay for Sidebar -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Sidebar -->
    <div class="sidebar d-flex flex-column" id="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header text-center mb-3 shadow-sm">
            <div class="d-flex align-items-center justify-content-center">
                <i class="bi bi-briefcase-fill me-2 fs-4"></i>
                <span>مكتب المحاماة</span>
            </div>
            <small class="d-block mt-1 opacity-75">نظام إدارة شامل</small>
        </div>

        <!-- User Info (Mobile) -->
        <div class="d-lg-none px-3 mb-3">
            {% if user.is_authenticated %}
            <div class="card bg-light text-dark">
                <div class="card-body p-2 text-center">
                    <i class="bi bi-person-circle fs-3"></i>
                    <div class="small fw-bold">{{ user.get_full_name|default:user.username }}</div>
                    <div class="small text-muted">{{ user.email }}</div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Navigation -->
        <nav class="nav flex-column px-2 flex-grow-1">
            <a class="nav-link {% if request.path == '/' or '/dashboard' in request.path %}active{% endif %}" href="/" data-bs-toggle="tooltip" data-bs-placement="left" title="لوحة التحكم الرئيسية">
                <i class="bi bi-house"></i>
                <span>لوحة التحكم</span>
            </a>
            <a class="nav-link {% if '/cases/' in request.path %}active{% endif %}" href="/cases/" data-bs-toggle="tooltip" data-bs-placement="left" title="إدارة القضايا">
                <i class="bi bi-briefcase"></i>
                <span>القضايا</span>
            </a>
            <a class="nav-link {% if '/clients/' in request.path %}active{% endif %}" href="/clients/" data-bs-toggle="tooltip" data-bs-placement="left" title="إدارة العملاء">
                <i class="bi bi-people"></i>
                <span>العملاء</span>
            </a>
            <a class="nav-link {% if '/appointments/' in request.path %}active{% endif %}" href="/appointments/" data-bs-toggle="tooltip" data-bs-placement="left" title="جدولة المواعيد">
                <i class="bi bi-calendar-event"></i>
                <span>المواعيد</span>
            </a>
            <a class="nav-link {% if '/documents/' in request.path %}active{% endif %}" href="/documents/" data-bs-toggle="tooltip" data-bs-placement="left" title="إدارة المستندات">
                <i class="bi bi-file-earmark-text"></i>
                <span>المستندات</span>
            </a>
            <a class="nav-link {% if '/reports/' in request.path %}active{% endif %}" href="/reports/" data-bs-toggle="tooltip" data-bs-placement="left" title="التقارير والإحصائيات">
                <i class="bi bi-bar-chart"></i>
                <span>التقارير</span>
            </a>
            <a class="nav-link {% if '/reports/accounts' in request.path %}active{% endif %}" href="/reports/accounts/" data-bs-toggle="tooltip" data-bs-placement="left" title="تقرير الحسابات المالية">
                <i class="bi bi-cash-coin"></i>
                <span>تقرير الحسابات</span>
            </a>
            <a class="nav-link {% if '/search/' in request.path %}active{% endif %}" href="/search/advanced/" data-bs-toggle="tooltip" data-bs-placement="left" title="البحث المتقدم">
                <i class="bi bi-search"></i>
                <span>بحث متقدم</span>
            </a>
            <a class="nav-link {% if '/calendar/' in request.path %}active{% endif %}" href="/calendar/" data-bs-toggle="tooltip" data-bs-placement="left" title="التقويم العام">
                <i class="bi bi-calendar3"></i>
                <span>التقويم</span>
            </a>
            <a class="nav-link {% if '/settings/' in request.path %}active{% endif %}" href="/settings/edit/" data-bs-toggle="tooltip" data-bs-placement="left" title="إعدادات النظام">
                <i class="bi bi-gear"></i>
                <span>الإعدادات</span>
            </a>
            <a class="nav-link {% if '/admin/activitylog/' in request.path %}active{% endif %}" href="/admin/activitylog/activitylog/" data-bs-toggle="tooltip" data-bs-placement="left" title="سجل النشاطات">
                <i class="bi bi-clock-history"></i>
                <span>سجل النشاطات</span>
            </a>
        </nav>

        <!-- Sidebar Footer -->
        <div class="mt-auto p-3 border-top border-light border-opacity-25">
            <a class="nav-link text-center" href="/authentication/logout/" onclick="return confirm('هل تريد تسجيل الخروج؟')">
                <i class="bi bi-box-arrow-right me-2"></i>
                <span>تسجيل الخروج</span>
            </a>
        </div>
    </div>
    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Top Navigation Bar -->
        <nav class="navbar navbar-expand-lg navbar-light px-3 sticky-top">
            <div class="container-fluid">
                <!-- Mobile Menu Toggle -->
                <button class="btn btn-outline-primary d-lg-none me-3" type="button" onclick="toggleSidebar()" aria-label="Toggle navigation">
                    <i class="bi bi-list"></i>
                </button>

                <!-- Search Bar (Desktop) -->
                <div class="d-none d-md-flex flex-grow-1 me-3">
                    <div class="input-group" style="max-width: 400px;">
                        <span class="input-group-text bg-light border-end-0">
                            <i class="bi bi-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control border-start-0 search-input" placeholder="البحث في النظام..." aria-label="البحث">
                    </div>
                </div>

                <!-- Right Side Controls -->
                <div class="d-flex align-items-center gap-2">
                    <!-- Theme Toggle -->
                    <button class="btn btn-outline-secondary theme-toggle" onclick="toggleTheme()" title="تبديل الوضع الليلي/النهاري" aria-label="تبديل الوضع">
                        <svg id="themeIcon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-moon" viewBox="0 0 16 16">
                            <path d="M6 0a7 7 0 0 0 0 14c3.866 0 7-3.134 7-7 0-.256-.012-.51-.035-.762A6.978 6.978 0 0 1 6 0z"/>
                        </svg>
                    </button>

                    <!-- Notifications -->
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary position-relative" type="button" id="notificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false" aria-label="الإشعارات">
                            <i class="bi bi-bell fs-6"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge" id="notif-count" style="{% if notifications_unread_count == 0 %}display: none;{% endif %}">
                                {{ notifications_unread_count|default:0 }}
                            </span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end shadow-lg" aria-labelledby="notificationsDropdown" style="min-width: 320px; max-height: 400px; overflow-y: auto;">
                            <li class="dropdown-header d-flex justify-content-between align-items-center">
                                <span class="fw-bold">الإشعارات</span>
                                {% if notifications_unread_count > 0 %}
                                <small class="text-muted">{{ notifications_unread_count }} غير مقروءة</small>
                                {% endif %}
                            </li>
                            {% if notifications %}
                                {% for notif in notifications|slice:":5" %}
                                <li>
                                    <a href="{% url 'notifications:mark_as_read_and_redirect' notif.pk %}" class="dropdown-item {% if not notif.is_read %}bg-light{% endif %} py-2">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0 me-2">
                                                <i class="bi bi-{% if notif.notification_type == 'appointment' %}calendar-event text-primary{% elif notif.notification_type == 'case' %}briefcase text-success{% elif notif.notification_type == 'document' %}file-text text-info{% else %}bell text-warning{% endif %}"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="fw-bold small">{{ notif.title }}</div>
                                                <div class="text-muted small">{{ notif.message|truncatechars:60 }}</div>
                                                <small class="text-muted">{{ notif.created_at|timesince }} مضت</small>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                {% endfor %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center fw-bold" href="{% url 'notifications:list' %}">عرض كل الإشعارات</a></li>
                            {% else %}
                                <li class="dropdown-item text-center text-muted py-4">
                                    <i class="bi bi-bell-slash fs-4 d-block mb-2"></i>
                                    لا توجد إشعارات
                                </li>
                            {% endif %}
                        </ul>
                    </div>

                    <!-- User Menu -->
                    {% if user.is_authenticated %}
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-1"></i>
                            <span class="d-none d-sm-inline">{{ user.get_full_name|default:user.username }}</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><h6 class="dropdown-header">{{ user.get_full_name|default:user.username }}</h6></li>
                            <li><span class="dropdown-item-text small text-muted">{{ user.email }}</span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/settings/profile/"><i class="bi bi-person me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="/settings/edit/"><i class="bi bi-gear me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="/authentication/logout/" onclick="return confirm('هل تريد تسجيل الخروج؟')"><i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </div>
        </nav>

        <!-- Breadcrumb Navigation -->
        {% block breadcrumb %}
        <nav aria-label="breadcrumb" class="px-3 pt-2">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/" class="text-decoration-none">الرئيسية</a></li>
                {% block breadcrumb_items %}{% endblock %}
            </ol>
        </nav>
        {% endblock %}

        <!-- Main Content Wrapper -->
        <div class="content-wrapper">
            <!-- Messages/Alerts -->
            {% if messages %}
            <div class="container-fluid">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show animate__animated animate__fadeInDown" role="alert">
                    <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Page Content -->
            {% block content %}{% endblock %}
        </div>

        <!-- Footer -->
        <footer class="bg-light border-top mt-5 py-4">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-0 text-muted">&copy; {% now "Y" %} نظام مكتب المحاماة. جميع الحقوق محفوظة.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <small class="text-muted">
                            الإصدار 1.0 |
                            <a href="/help/" class="text-decoration-none">المساعدة</a> |
                            <a href="/contact/" class="text-decoration-none">اتصل بنا</a>
                        </small>
                    </div>
                </div>
            </div>
        </footer>
    </div>
    <!-- JavaScript Libraries -->
    <!-- Bootstrap Bundle JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery (for DataTables and other plugins) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- DataTables (for enhanced tables) -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>

    <!-- Chart.js (for dashboard charts) -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom JavaScript -->
    <script src="{% static 'js/main.js' %}"></script>

    <!-- Page-specific JavaScript -->
    {% block extra_js %}{% endblock %}

    <!-- Service Worker Registration (for PWA) -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>

    <!-- Global Error Handler -->
    <script>
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
            // You can send error reports to your logging service here
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
            e.preventDefault();
        });
    </script>

    <!-- CSRF Token for AJAX requests -->
    <script>
        // Make CSRF token available globally
        window.csrfToken = '{{ csrf_token }}';

        // Set up CSRF token for all AJAX requests
        if (typeof $ !== 'undefined') {
            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!this.crossDomain) {
                        xhr.setRequestHeader("X-CSRFToken", window.csrfToken);
                    }
                }
            });
        }
    </script>
</body>
</html>
