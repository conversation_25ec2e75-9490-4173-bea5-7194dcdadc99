{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام مكتب المحاماة{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body[data-theme='dark'] {
            background: linear-gradient(135deg, #232526 0%, #414345 100%);
            color: #f1f1f1;
        }
        body[data-theme='light'] {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e6ea 100%);
            color: #222b45;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #222b45 0%, #2b365e 100%);
            color: #fff;
            width: 230px;
            position: fixed;
            top: 0; right: 0;
            z-index: 1030;
            transition: all 0.2s;
            box-shadow: -2px 0 12px rgba(34,43,69,0.08);
        }
        .sidebar .nav-link {
            color: #fff;
            font-weight: 500;
            border-radius: 0.5rem;
            margin-bottom: 0.2rem;
            transition: background 0.2s, color 0.2s;
        }
        .sidebar .nav-link.active, .sidebar .nav-link:hover {
            background: linear-gradient(90deg, #ffd600 0%, #ffef8a 100%);
            color: #222b45;
        }
        .sidebar .nav-link i { margin-left: 8px; }
        .sidebar .sidebar-header {
            font-size: 1.3rem;
            font-weight: bold;
            padding: 1.5rem 1rem 1rem;
            letter-spacing: 1px;
            background: linear-gradient(90deg, #ffd600 0%, #ffef8a 100%);
            color: #222b45;
            border-radius: 0 0 1rem 1rem;
            margin-bottom: 1.5rem;
        }
        .main-content {
            margin-right: 230px;
            min-height: 100vh;
            transition: all 0.2s;
        }
        .navbar {
            border-bottom: 1px solid #eee;
            background: linear-gradient(90deg, #fffbe6 0%, #f8fafc 100%);
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        }
        .theme-toggle {
            cursor: pointer;
            border-radius: 50%;
            padding: 0.3rem;
            transition: background 0.2s;
        }
        .theme-toggle:hover {
            background: #ffd60033;
        }
        .content-wrapper {
            padding: 2.5rem 1.5rem 2rem 1.5rem;
            min-height: 80vh;
        }
        .dropdown-menu {
            border-radius: 1rem;
            box-shadow: 0 4px 24px rgba(34,43,69,0.08);
        }
        .badge.bg-danger {
            animation: pulse 1.2s infinite alternate;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 #ff5252aa; }
            100% { box-shadow: 0 0 0 8px #ff525200; }
        }
        @media (max-width: 991px) {
            .sidebar { width: 100vw; min-height: auto; position: static; }
            .main-content { margin-right: 0; }
        }
        @media (max-width: 575px) {
            .content-wrapper { padding: 1rem 0.2rem; }
            .sidebar .sidebar-header { font-size: 1.1rem; padding: 1rem 0.5rem 0.5rem; }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body data-theme="light">
    <div class="sidebar d-flex flex-column">
        <div class="sidebar-header text-center mb-3 shadow-sm">مكتب المحاماة</div>
        <nav class="nav flex-column">
            <a class="nav-link {% if request.path == '/' or '/dashboard' in request.path %}active{% endif %}" href="/"> <i class="bi bi-house"></i>لوحة التحكم</a>
            <a class="nav-link {% if '/cases/' in request.path %}active{% endif %}" href="/cases/"> <i class="bi bi-briefcase"></i>القضايا</a>
            <a class="nav-link {% if '/clients/' in request.path %}active{% endif %}" href="/clients/"> <i class="bi bi-people"></i>العملاء</a>
            <a class="nav-link {% if '/appointments/' in request.path %}active{% endif %}" href="/appointments/"> <i class="bi bi-calendar-event"></i>المواعيد</a>
            <a class="nav-link {% if '/documents/' in request.path %}active{% endif %}" href="/documents/"> <i class="bi bi-file-earmark-text"></i>المستندات</a>
            <a class="nav-link {% if '/reports/' in request.path %}active{% endif %}" href="/reports/"> <i class="bi bi-bar-chart"></i>التقارير</a>
            <a class="nav-link {% if '/reports/accounts' in request.path %}active{% endif %}" href="/reports/accounts/"> <i class="bi bi-cash-coin"></i>تقرير الحسابات</a>
            <a class="nav-link {% if '/search/' in request.path %}active{% endif %}" href="/search/advanced/"> <i class="bi bi-search"></i>بحث متقدم</a>
            <a class="nav-link {% if '/settings/' in request.path %}active{% endif %}" href="/settings/edit/"> <i class="bi bi-gear"></i>الإعدادات</a>
            <a class="nav-link {% if '/admin/activitylog/' in request.path %}active{% endif %}" href="/admin/activitylog/activitylog/"> <i class="bi bi-clock-history"></i>سجل النشاطات</a>
            <a class="nav-link {% if '/calendar/' in request.path %}active{% endif %}" href="/calendar/"> <i class="bi bi-calendar3"></i>التقويم</a>
            <a class="nav-link mt-3" href="/authentication/logout/"> <i class="bi bi-box-arrow-right"></i>تسجيل الخروج</a>
        </nav>
    </div>
    <div class="main-content">
        <nav class="navbar navbar-expand-lg navbar-light px-3">
            <div class="container-fluid">
                <span class="theme-toggle me-3" onclick="toggleTheme()" title="تبديل الوضع الليلي/النهاري">
                    <svg id="themeIcon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-moon" viewBox="0 0 16 16">
                        <path d="M6 0a7 7 0 0 0 0 14c3.866 0 7-3.134 7-7 0-.256-.012-.51-.035-.762A6.978 6.978 0 0 1 6 0z"/>
                    </svg>
                </span>
                <!-- إشعارات -->
                <div class="dropdown">
                    <a class="btn position-relative" href="#" id="notificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-bell fs-5"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notif-count">
                            {{ notifications_unread_count|default:0 }}
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown" style="min-width: 300px; max-height: 350px; overflow-y: auto;">
                        {% if notifications %}
                            {% for notif in notifications|slice:":5" %}
                            <li class="dropdown-item small {% if not notif.is_read %}fw-bold{% endif %}">
                                <a href="{% url 'notifications:mark_as_read_and_redirect' notif.pk %}" class="text-decoration-none d-block">
                                    <div>{{ notif.title }}</div>
                                    <small class="text-muted">{{ notif.created_at|date:"Y-m-d H:i" }}</small>
                                </a>
                            </li>
                            {% endfor %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="{% url 'notifications:list' %}">عرض كل الإشعارات</a></li>
                        {% else %}
                            <li class="dropdown-item text-center text-muted">لا توجد إشعارات</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>
        <div class="content-wrapper">
            {% block content %}{% endblock %}
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleTheme() {
            const body = document.body;
            const icon = document.getElementById('themeIcon');
            if (body.getAttribute('data-theme') === 'dark') {
                body.setAttribute('data-theme', 'light');
                icon.innerHTML = '<path d="M6 0a7 7 0 0 0 0 14c3.866 0 7-3.134 7-7 0-.256-.012-.51-.035-.762A6.978 6.978 0 0 1 6 0z"/>';
            } else {
                body.setAttribute('data-theme', 'dark');
                icon.innerHTML = '<path d="M12.293 9.293a1 1 0 0 1 1.414 1.414A7 7 0 1 1 6 0a1 1 0 0 1 0 2 5 5 0 1 0 5 5 1 1 0 0 1 1.293 2.293z"/>';
            }
        }
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
