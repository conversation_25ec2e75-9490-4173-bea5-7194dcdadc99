{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="fw-bold"><i class="bi bi-briefcase text-primary me-2"></i>القضايا</h3>
        <a href="{% url 'cases:add' %}" class="btn btn-gradient-gold">
            <i class="bi bi-plus-circle"></i> إضافة قضية جديدة
        </a>
    </div>
    <div class="card shadow rounded-4 border-0 p-3">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th><i class="bi bi-hash"></i> رقم القضية</th>
                        <th><i class="bi bi-type"></i> العنوان</th>
                        <th><i class="bi bi-person"></i> العميل</th>
                        <th><i class="bi bi-person-badge"></i> المحامي</th>
                        <th><i class="bi bi-info-circle"></i> الحالة</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for case in cases %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td><a href="{% url 'cases:detail' case.pk %}" class="fw-bold text-decoration-none">{{ case.case_number }}</a></td>
                        <td>{{ case.title }}</td>
                        <td>{{ case.client }}</td>
                        <td>{{ case.lawyer }}</td>
                        <td><span class="badge bg-primary bg-opacity-10 text-primary fs-6">{{ case.get_status_display }}</span></td>
                        <td>
                            <a href="{% url 'cases:edit' case.pk %}" class="btn btn-sm btn-outline-primary"><i class="bi bi-pencil-square"></i></a>
                            <a href="{% url 'cases:delete' case.pk %}" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr><td colspan="7" class="text-center">لا يوجد قضايا بعد.</td></tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
<style>
.btn-gradient-gold {
    background: linear-gradient(90deg, #ffd600 0%, #ffb300 100%);
    color: #222b45;
    border: none;
    font-weight: bold;
    transition: box-shadow 0.2s;
}
.btn-gradient-gold:hover {
    box-shadow: 0 2px 12px #ffd60055;
    color: #151a30;
}
</style>
{% endblock %} 