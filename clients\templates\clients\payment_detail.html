{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-md-7 col-lg-6">
            <div class="card shadow rounded-4 border-0 p-4">
                <h4 class="mb-3 fw-bold"><i class="bi bi-eye text-info me-2"></i>تفاصيل الدفعة</h4>
                <ul class="list-group list-group-flush mb-3">
                    <li class="list-group-item">العميل: <strong>{{ client.full_name }}</strong></li>
                    <li class="list-group-item">المبلغ: <strong>{{ payment.amount|floatformat:2 }}</strong></li>
                    <li class="list-group-item">التاريخ: <strong>{{ payment.date }}</strong></li>
                    <li class="list-group-item">القضية: <strong>{% if payment.case %}{{ payment.case.name }}{% else %}-{% endif %}</strong></li>
                    <li class="list-group-item">ملاحظات: <strong>{{ payment.notes|default:'-' }}</strong></li>
                </ul>
                <div class="d-flex justify-content-between gap-2">
                    <a href="{% url 'clients:edit_payment' payment.id %}" class="btn btn-outline-primary flex-fill"><i class="bi bi-pencil"></i> تعديل</a>
                    <a href="{% url 'clients:delete_payment' payment.id %}" class="btn btn-outline-danger flex-fill"><i class="bi bi-trash"></i> حذف</a>
                    <a href="{% url 'clients:client_payments' client.id %}" class="btn btn-link flex-fill"><i class="bi bi-arrow-right-circle"></i> العودة للدفعات</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 