{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10">
            <div class="card shadow rounded-4 border-0">
                <div class="card-body p-4">
                    <h3 class="mb-4 text-center fw-bold">
                        <i class="bi bi-cash-coin text-primary me-2"></i>
                        تقرير الحسابات الشامل لكل العملاء
                    </h3>
                    <div class="d-flex justify-content-end mb-3">
                        <a href="{% url 'reports:accounts_summary_excel' %}" class="btn btn-success btn-sm px-4 ms-2">
                            <i class="bi bi-file-earmark-excel"></i> تصدير Excel
                        </a>
                        <a href="{% url 'reports:accounts_summary_pdf' %}" class="btn btn-danger btn-sm px-4 ms-2">
                            <i class="bi bi-file-earmark-pdf"></i> تصدير PDF
                        </a>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-4 mb-2">
                            <div class="alert alert-success text-center mb-0">إجمالي المدفوع: <strong>{{ total_paid|floatformat:2 }}</strong> ج.م</div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="alert alert-warning text-center mb-0">إجمالي المتبقي: <strong>{{ total_unpaid|floatformat:2 }}</strong> ج.م</div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="alert alert-info text-center mb-0">إجمالي الأتعاب: <strong>{{ total_fees|floatformat:2 }}</strong> ج.م</div>
                        </div>
                    </div>
                    <h5 class="fw-bold mt-4 mb-3"><i class="bi bi-people me-2"></i>تفاصيل العملاء</h5>
                    {% if clients_data %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover align-middle text-center">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم العميل</th>
                                        <th>إجمالي الأتعاب</th>
                                        <th>المدفوع</th>
                                        <th>المتبقي</th>
                                        <th>تقرير مفصل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in clients_data %}
                                    <tr>
                                        <td>{{ item.client.full_name }}</td>
                                        <td>{{ item.total|floatformat:2 }}</td>
                                        <td>{{ item.paid|floatformat:2 }}</td>
                                        <td>{{ item.unpaid|floatformat:2 }}</td>
                                        <td>
                                            <a href="{% url 'clients:accounts_report' item.client.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i> عرض تقرير العميل
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info text-center">لا يوجد عملاء مسجلين.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
{% endblock %} 