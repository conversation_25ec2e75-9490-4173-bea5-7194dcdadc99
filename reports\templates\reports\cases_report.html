{% extends 'base.html' %}
{% block content %}
<div class="container py-4 animate__animated animate__fadeIn">
    <h3 class="mb-4 fw-bold"><i class="bi bi-briefcase text-primary me-2"></i>تقرير القضايا حسب الحالة</h3>
    <div class="mb-3 d-flex flex-wrap gap-2">
        <a href="{% url 'reports:cases_report_pdf' %}?{% if selected_status %}status={{ selected_status }}{% endif %}" class="btn btn-outline-danger">تصدير PDF</a>
        <a href="{% url 'reports:cases_report_excel' %}?{% if selected_status %}status={{ selected_status }}{% endif %}" class="btn btn-outline-success">تصدير Excel</a>
    </div>
    <div class="mb-4">
        <canvas id="casesChart" height="100"></canvas>
    </div>
    <form method="get" class="row g-3 mb-4">
        <div class="col-md-4">
            <select name="status" class="form-select" onchange="this.form.submit()">
                <option value="">كل الحالات</option>
                {% for value, label in statuses %}
                    <option value="{{ value }}" {% if value == selected_status %}selected{% endif %}>{{ label }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-primary w-100">تصفية</button>
        </div>
    </form>
    <div class="card p-3 shadow rounded-4 border-0">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>رقم القضية</th>
                        <th>العنوان</th>
                        <th>العميل</th>
                        <th>المحامي</th>
                        <th>تاريخ البدء</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for case in cases %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ case.case_number }}</td>
                        <td>{{ case.title }}</td>
                        <td>{{ case.client }}</td>
                        <td>{{ case.lawyer }}</td>
                        <td>{{ case.start_date }}</td>
                        <td>{{ case.get_status_display }}</td>
                    </tr>
                    {% empty %}
                    <tr><td colspan="7" class="text-center">لا توجد قضايا مطابقة.</td></tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
{% endblock %}
{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // رسم بياني Chart.js
    const chartData = JSON.parse('{{ chart_data|safe|escapejs }}');
    const ctx = document.getElementById('casesChart').getContext('2d');
    const labels = chartData.map(item => item.label);
    const data = chartData.map(item => item.count);
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    '#0d6efd', '#198754', '#ffc107', '#dc3545', '#6c757d', '#6610f2', '#fd7e14'
                ],
            }]
        },
        options: {
            plugins: {
                legend: { display: true, position: 'bottom' }
            }
        }
    });
</script>
{% endblock %} 