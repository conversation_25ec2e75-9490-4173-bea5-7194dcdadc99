from django.shortcuts import render
from django.urls import reverse_lazy, reverse
from django.views.generic import ListView, CreateView, UpdateView, DetailView, DeleteView
from .models import Appointment
from notifications.utils import create_notification
from django.contrib.auth.mixins import PermissionRequiredMixin
from activitylog.utils import log_activity
from rest_framework import viewsets, permissions
from .serializers import AppointmentSerializer
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters

# Create your views here.

class AppointmentListView(ListView):
    model = Appointment
    template_name = 'appointments/appointment_list.html'
    context_object_name = 'appointments'

class AppointmentCreateView(PermissionRequiredMixin, CreateView):
    permission_required = 'appointments.add_appointment'
    model = Appointment
    fields = ['title', 'case', 'client', 'lawyer', 'datetime', 'location', 'notes']
    template_name = 'appointments/appointment_form.html'
    success_url = reverse_lazy('appointments:list')

    def form_valid(self, form):
        response = super().form_valid(form)
        appt = self.object
        # إشعار للمحامي فقط
        if appt.lawyer:
            create_notification(
                user=appt.lawyer,
                title='تم تعيينك على موعد جديد',
                message=f'تم تعيينك على موعد "{appt.title}".',
                url=reverse('appointments:detail', args=[appt.pk]),
                notification_type='appointment',
                send_email=True
            )
        log_activity(self.request.user, 'appointment', appt.pk, 'create', f'إضافة موعد: {appt.title}')
        return response

class AppointmentUpdateView(PermissionRequiredMixin, UpdateView):
    permission_required = 'appointments.change_appointment'
    model = Appointment
    fields = ['title', 'case', 'client', 'lawyer', 'datetime', 'location', 'notes']
    template_name = 'appointments/appointment_form.html'
    success_url = reverse_lazy('appointments:list')

    def form_valid(self, form):
        response = super().form_valid(form)
        appt = self.object
        # إشعار للمحامي فقط
        if appt.lawyer:
            create_notification(
                user=appt.lawyer,
                title='تم تعديل موعد يخصك',
                message=f'تم تعديل بيانات الموعد "{appt.title}".',
                url=reverse('appointments:detail', args=[appt.pk]),
                notification_type='appointment',
                send_email=True
            )
        log_activity(self.request.user, 'appointment', appt.pk, 'update', f'تعديل موعد: {appt.title}')
        return response

class AppointmentDetailView(DetailView):
    model = Appointment
    template_name = 'appointments/appointment_detail.html'
    context_object_name = 'appointment'

class AppointmentDeleteView(PermissionRequiredMixin, DeleteView):
    permission_required = 'appointments.delete_appointment'
    model = Appointment
    template_name = 'appointments/appointment_confirm_delete.html'
    success_url = reverse_lazy('appointments:list')

    def delete(self, request, *args, **kwargs):
        appt = self.get_object()
        log_activity(request.user, 'appointment', appt.pk, 'delete', f'حذف موعد: {appt.title}')
        return super().delete(request, *args, **kwargs)

class IsAppointmentOwnerOrAdmin(permissions.BasePermission):
    def has_object_permission(self, request, view, obj):
        if request.user.is_superuser or request.user.groups.filter(name='Admin').exists():
            return True
        if obj.lawyer == request.user:
            return True
        return False

class AppointmentViewSet(viewsets.ModelViewSet):
    queryset = Appointment.objects.all()
    serializer_class = AppointmentSerializer
    permission_classes = [permissions.IsAuthenticated, IsAppointmentOwnerOrAdmin]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['case', 'client', 'lawyer', 'datetime']
    search_fields = ['title', 'location', 'notes']
    ordering_fields = ['title', 'datetime']
