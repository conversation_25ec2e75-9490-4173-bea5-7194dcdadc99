from django.urls import path, include
from .views import (
    DocumentListView, DocumentCreateView, DocumentUpdateView, DocumentDetailView, DocumentDeleteView, DocumentViewSet
)
from rest_framework.routers import DefaultRouter

app_name = 'documents'

router = DefaultRouter()
router.register(r'api/documents', DocumentViewSet, basename='api-documents')

urlpatterns = [
    path('', DocumentListView.as_view(), name='list'),
    path('add/', DocumentCreateView.as_view(), name='add'),
    path('<int:pk>/edit/', DocumentUpdateView.as_view(), name='edit'),
    path('<int:pk>/', DocumentDetailView.as_view(), name='detail'),
    path('<int:pk>/delete/', DocumentDeleteView.as_view(), name='delete'),
] + router.urls 